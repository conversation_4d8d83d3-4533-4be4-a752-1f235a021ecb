<script lang="ts" setup>
import PageHeader from 'src/components/common/PageHeader.vue';
import { useGlobalStore } from 'src/stores/global';
import { useUserStore } from 'src/stores/user';
import { useI18n } from 'vue-i18n';
import useFilterStore from 'src/stores/filter-search';
import ActionsCell from 'src/components/table/ActionsCell.vue';
import { userColumns } from 'src/data/column_table';
import { useQuasar } from 'quasar';
import UserForm from 'src/features/user/dialog/UserForm.vue';

const store = useUserStore();
const filterStore = useFilterStore();
const { t } = useI18n();
const global = useGlobalStore();
const $q = useQuasar();

const onClickAdd = () => {
  store.resetForm();
  store.titleForm = 'form.New User';
  $q.dialog({
    component: UserForm,
  });
};

</script>

<template>
  <q-page padding>
    <PageHeader
      v-model:search-text="filterStore.search"
      :label-search="t('search') + t('users')"
      @open-dialog="onClickAdd"
      @enter-search="store.fetchAll"
    />
    <q-table
      flat
      bordered
      :loading="global.getLoadingState"
      class="q-mt-md q-animate--fade page-table"
      :rows="store.users"
      :columns="userColumns"
      row-key="id"
      wrap-cells
      separator="cell"
      :pagination="store.pagination"
      @update:pagination="store.fetchAll"
      @request="(r) => store.fetchAll(r.pagination)"
    >
      <template #body-cell-number="props">
        <q-td>
          {{ props.rowIndex + 1 }}
        </q-td>
      </template>
      <template #body-cell-actions="props">
        <ActionsCell
          @handle-edit="
            store.toggleDialog({ form: props.row, title: 'form.Edit User' })
          "
          @handle-delete="store.handleDelete(props.row.id)"
        />
      </template>
    </q-table>
  </q-page>
</template>
