{"name": "skill-mapping", "version": "2025-0.3.0", "description": "Burapha University Project", "productName": "Skill Mapping", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "type": "module", "dependencies": {"@quasar/extras": "^1.16.17", "@types/lodash": "^4.17.16", "axios": "^1.8.4", "caniuse-lite": "^1.0.30001714", "d3": "^7.9.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "pinia": "^2.3.1", "quasar": "^2.18.1", "quasar-extras-svg-icons": "^2.2.0", "register-service-worker": "^1.7.2", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-json-pretty": "^2.4.0", "vue-router": "^4.5.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@eslint/js": "^9.24.0", "@quasar/app-vite": "^2.2.0", "@types/d3": "^7.4.3", "@types/node": "^20.17.30", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-plugin-vue": "^9.33.0", "globals": "^15.15.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "vite-plugin-checker": "^0.8.0", "vue-tsc": "^2.2.8", "workbox-build": "^7.3.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}