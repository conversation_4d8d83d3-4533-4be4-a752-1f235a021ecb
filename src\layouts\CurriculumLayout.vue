<template>
  <q-layout view="hHh lpR fFf">
    <AppHeader hide-toggle> </AppHeader>
    <AppDrawer>
      <q-list class="q-my-lg">
        <MenuLink v-for="link in menuList" :key="link.title" v-bind="link" />
        <q-separator class="q-my-md" />
        <q-item clickable class="btn-menu" @click="handleDelete(currCode)">
          <q-item-section avatar>
            <q-icon name="warning" color="negative" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-negative">{{
              t('deleteCurriculum')
            }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </AppDrawer>
    <q-page-container>
      <q-toolbar class="q-px-md">
        <q-breadcrumbs>
          <q-breadcrumbs-el :label="t('home')" icon="home" to="/" />
          <q-breadcrumbs-el
            :label="`${t('curriculum')}`"
            :to="`/curriculums/management`"
          />
          <q-breadcrumbs-el class="text-weight-medium">
            <q-tooltip>{{ currentBreadcrumb }}</q-tooltip>
            <span class="ellipsis" style="max-width: 1000px">{{
              currentBreadcrumb
            }}</span>
          </q-breadcrumbs-el>
        </q-breadcrumbs>
      </q-toolbar>
      <div class="q-px-md">
        <q-separator class="q-mb-md"></q-separator>
        <router-view />
      </div>
    </q-page-container>
    <AppRightDrawer />
  </q-layout>
</template>

<script lang="ts" setup>
import AppDrawer from 'src/components/ui/AppLeftDrawer.vue';
import AppHeader from 'src/components/ui/AppHeader.vue';
import MenuLink, { type MenuProps } from 'src/components/ui/MenuLink.vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import { useQuasar } from 'quasar';
import { useCurriculumStore } from 'src/stores/curriculum';
import { CurriculumService } from 'src/services/curriculums';
import AppRightDrawer from 'src/components/ui/AppRightDrawer.vue';
import { ppStudentFill } from 'quasar-extras-svg-icons/phosphor-icons-v2'

const route = useRoute();
const curriculumStore = useCurriculumStore();
const router = useRouter();
const { t } = useI18n();
const auth = useAuthStore();
const currCode = computed(() => route.params.code as string);
const basePath = `/curriculums/${currCode.value}`;
const $q = useQuasar();
const currentBreadcrumb = computed(() => {
  return `${t('code')} ${currCode.value} ${curriculumStore.getCurriculum.thaiName}`;
});

const handleDelete = (code: string) => {
  const confirmDelete = $q.dialog({
    title: t('dialog.deleteConfirmTitle'),
    message: `${t('dialog.deleteConfirm', { code })} ?`,
    persistent: true,
    ok: {
      label: t('delete'),
      color: 'negative',
      flat: true,
    },
    cancel: {
      label: t('cancel'),
      color: 'grey',
      flat: true,
    },
  });

  confirmDelete.onOk(() => {
    const currId = useCurriculumStore().getInsertId;
    if (!currId) {
      $q.notify({
        type: 'negative',
        message: t('error'),
      });
      return;
    }
    CurriculumService.removeOne(currId)
      .then(() => {
        $q.notify({
          type: 'positive',
          message: t('notify.deleteSuccess'),
        });
        router
          .push({
            path: `/${auth.getRole}/curriculums`,
          })
          .then(() => {})
          .catch((error) => {
            $q.notify({
              type: 'negative',
              message: error.message,
            });
          });
      })
      .catch((error) => {
        $q.notify({
          type: 'negative',
          message: error.message,
        });
      });
  });
};

const menuList: MenuProps[] = [
  {
    icon: 'home',
    title: 'curriculum',
    link: basePath,
  },
  {
    icon: 'collections_bookmark',
    title: 'PLOs',
    link: `${basePath}/plos`,
  },
  {
    icon: 'code',
    title: 'skills',
    link: `${basePath}/skills`,
  },
  {
    icon: 'books',
    title: 'subjects',
    link: `${basePath}/subjects`,
  },
  {
    icon: 'persons',
    title: 'coordinators',
    link: `${basePath}/coordinators`,
  },
  {
    icon: ppStudentFill,
    title: 'students',
    link: `${basePath}/students`,
  },
  {
    title: 'courses',
    icon: 'play_lesson',
    link: `${basePath}/courses`,
  },
  {
    icon: 'check',
    title: 'summary',
    link: `${basePath}/summary`,
  },
    {
    icon: 'check',
    title: 'summary2',
    link: `${basePath}/summary2`,
  },
];

defineOptions({
  name: 'CurriculumLayout',
});
</script>

<style scoped>
.ellipsis {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: bottom;
}
</style>
