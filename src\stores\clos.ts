import { defineStore } from 'pinia';
import { ClosService } from 'src/services/clos';
import { useSubjectStore } from './subject';
import { useCurriculumStore } from './curriculum';
import { defaultPagination } from 'src/utils/pagination';
import type { CLO } from 'src/types/education';

type TitleForm = 'form.New CLO' | 'form.Edit CLO';

export const useCloStore = defineStore('clo', {
  state: () => ({
    dialogState: false,
    clos: [] as CLO[],
    form: {} as CLO,
    editMode: true,
    pagination: { ...defaultPagination },
    titleForm: '' as TitleForm,
    onlyHaveSubs: true,
    subjectStore: useSubjectStore(),
    currStore: useCurriculumStore(),
  }),
  getters: {
    getListClo: (c) => c.clos,
    getDialogTitle: (s) => s.titleForm,
  },
  actions: {
    async fetchAll(id: number) {
      const { data } = await ClosService.getAllBySubject(id);
      this.clos = JSON.parse(JSON.stringify(data));
    },
    async fetchOneData(id: number) {
      const data = await ClosService.getOne(id);
      this.form = data;
    },

    async handleOpenDialog(form?: Partial<CLO>) {
      if (form) {
        this.titleForm = 'form.Edit CLO';
        await this.fetchOneData(form.id || -1);
      } else {
        this.titleForm = 'form.New CLO';
        this.form = {} as CLO;
        this.form.name = `CLO ${this.clos.length + 1}`;
      }
      this.dialogState = true;
    },
    handleSave(subjectId: number) {
      this.form.subjectId = subjectId;
      if (this.titleForm === 'form.Edit CLO') {
        ClosService.updateOne(this.form)
          .then((data) => {
            this.updateTable(data, false);
          })
          .catch((err) => {
            console.error(err);
          });
      } else {
        ClosService.createOne(this.form)
          .then(async (ok) => {
            if (ok) {
              await this.fetchAll(subjectId);
            }
          })
          .catch((err) => {
            console.error(err);
          });
      }
      this.resetForm();
    },
    async removeOne(id: number) {
      const data = await ClosService.removeOne(id);
      this.updateTable(data, true);
    },
    updateTable(item: CLO, isDelete: boolean) {
      const index = this.clos.findIndex((clo) => clo.id === item.id);

      if (isDelete && index !== -1) {
        this.clos.splice(index, 1);
      } else {
        this.clos[index] = item;
      }
    },
    resetForm() {
      this.form = {} as CLO;
    },
  },
});
