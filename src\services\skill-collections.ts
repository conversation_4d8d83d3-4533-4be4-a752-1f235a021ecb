import { api } from 'boot/axios';
import type { SheetStudentScore, StudentScore } from 'src/types/sheetjs';
export class SkillCollectionService {
  static path = 'skill-collections';
  static async getAll(courseId: string, cloId: string) {
    const res = await api.get<StudentScore[]>(`${this.path}`, {
      params: {
        courseId,
        cloId,
      },
    });
    return res.data;
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`);
    return res.data;
  }

  static async postImportScores(
    courseId: string,
    cloId: string,
    scores: SheetStudentScore[],
  ) {
    const res = await api.post(`${this.path}/import`, scores, {
      params: { courseId, cloId },
    });
    return res.data;
  }
  static async fetchTranscript(studentCode: string) {
    const res = await api.get(`${this.path}/student`, {
      params: { studentCode },
    });
    return res.data;
  }
}
