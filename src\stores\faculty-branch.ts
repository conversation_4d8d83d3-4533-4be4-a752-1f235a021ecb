import { defineStore } from 'pinia';
import type { QTreeNode } from 'quasar';
import { Dialog, Notify } from 'quasar';
import { BranchService } from 'src/services/branch';
import { FacultyService } from 'src/services/faculty';
import type { QueryParams } from 'src/types/api';
import type { Faculty, Branch } from 'src/types/education';
import { isPrimarilyThai } from 'src/utils/language';

import {
  calMaxPage,
  convertToQuery,
  defaultPagination,
} from 'src/utils/pagination';

type TitleForm = 'form.New Faculty' | 'form.Edit Faculty' | 'form.New Branch' | 'form.Edit Branch';

export const useFacultyStore = defineStore('faculty', {
  state: () => ({
    formFaculty: {} as Partial<Faculty>,
    formBranch: {} as Partial<Branch>,
    faculties: [] as Faculty[],
    dialogState: false,
    pagination: { ...defaultPagination },
    search: '',
    titleForm: '' as TitleForm,
    qDialog: Dialog,
    qNotify: Notify,
    filterModel: {} as Partial<QueryParams>,
  }),
  getters: {
    getMaxPage: (s) =>
      calMaxPage(s.pagination?.rowsNumber, s.pagination?.rowsPerPage),
    isFacultyForm: (state) =>
      state.titleForm === 'form.New Faculty' || state.titleForm === 'form.Edit Faculty',
    getNodes: (state): QTreeNode[] => {
      if (state.faculties) {
        return state.faculties.map((faculty: Faculty) => {
          const { branch, ...rest } = faculty;
          return {
            ...rest,
            isFaculty: true,
            children:
              branch &&
              branch.map((branch: Branch) => ({
                ...branch,
                children: [],
              })),
          };
        });
      }
      return [];
    },
    getFaculties: (s) => s.faculties,
    getJsonForm: (state) => {
      if (
        state.titleForm === 'form.New Faculty' ||
        state.titleForm === 'form.Edit Faculty'
      ) {
        return state.formFaculty;
      } else {
        return state.formBranch;
      }
    },
  },
  actions: {
    async fetchData() {
      if (this.search.length === 0) {
        this.filterModel = {};
      } else {
        const isThai = isPrimarilyThai(this.search);
        if (isThai) {
          this.filterModel.thaiName = this.search;
        } else {
          this.filterModel.engName = this.search;
        }
      }
      const res = await FacultyService.getAll(
        convertToQuery(this.pagination, this.filterModel),
      );
      if (res.data) {
        this.faculties = res.data;
      }
    },
    async removeOne(id: string) {
      await FacultyService.removeOne(id);
      this.faculties = (await FacultyService.getAll()).data;
    },
    async handleSave() {
      let text = '';
      switch (this.titleForm) {
        case 'form.New Faculty':
          await FacultyService.createOne(this.formFaculty);
          text = 'Faculty created successfully';
          break;
        case 'form.Edit Faculty':
          await FacultyService.updateOne(this.formFaculty);
          text = 'Faculty updated successfully';
          break;
        case 'form.New Branch':
          await BranchService.createOne(this.formBranch);
          text = 'Branch created successfully';
          break;
        case 'form.Edit Branch':
          await BranchService.updateOne(this.formBranch);
          text = 'Branch updated successfully';
          break;
        default:
          break;
      }
      this.qNotify.create({
        type: 'ok',
        message: text,
      });
      this.dialogState = false;
      await this.fetchData();
      // this.fetchDataBranch()
    },
    handleRemove({
      id,
      node,
    }: {
      id: string;
      node: Partial<Faculty> | Partial<Branch>;
    }) {
      this.qDialog
        .create({
          title: 'Confirm',
          message: `Are you sure you want to delete this <div class="text-red text-bold col-12">${node.engName} ?</div>`,
          html: true,
          cancel: true,
          persistent: true,
        })
        .onCancel(() => {
          return;
        })
        .onOk(() => {
          const remove = async () => {
            if ('branch' in node) {
              await FacultyService.removeOne(id);
            } else {
              await BranchService.removeOne(id);
            }
            await this.fetchData();
          };
          remove()
            .then(() => {
              this.qNotify.create({
                type: 'positive',
                message: 'Faculty deleted successfully',
              });
            })
            .catch((err) => {
              this.qNotify.create({
                type: 'negative',
                message: err.message,
              });
            });
        });
    },
    resetForm() {
      this.formFaculty = {} as Partial<Faculty>;
      this.formBranch = {} as Partial<Branch>;
    },

    isFaculty(
      form: Partial<Faculty> | Partial<Branch>,
    ): form is Partial<Faculty> {
      return (form as Partial<Faculty>).branch !== undefined;
    },
  },
});
