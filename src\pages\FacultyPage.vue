<template>
  <q-page padding>
    <MainHeader
      v-model:search-text="store.search"
      @open-dialog="handleNewFaculty"
      hide-filter
      :label-add="t('newFaculty')"
      :label-search="t('search') + t('faculty')"
      @enter-search="store.fetchData"
    />
    <!-- Content -->
    <q-scroll-area class="q-mt-lg" style="height: calc(100vh - 170px)">
      <q-tree
        :nodes="store.faculties"
        node-key="thaiName"
        children-key="branch"
        :no-nodes-label="t('noData')"
      >
        <template #default-header="props">
          <q-tr class="full-width q-py-xs cursor-pointer hover-row">
            <q-td>
              <span class="text-body1">{{ props.node.thaiName }} </span>
            </q-td>
            <q-td class="q-gutter-x-sm">
              <q-btn
                @click="handleNewBranch(props.node)"
                icon="subdirectory_arrow_right"
                padding="none"
                class="hover-btn"
                flat
              >
                <q-tooltip>{{ t('addBranch') }}</q-tooltip>
              </q-btn>
              <q-btn
                @click="handleEdit(props.node)"
                icon="edit"
                padding="none"
                class="hover-btn"
                flat
              >
                <q-tooltip>{{ t('edit') }}</q-tooltip>
              </q-btn>
              <q-btn
                @click="
                  store.handleRemove({ id: props.node.id, node: props.node })
                "
                icon="delete"
                padding="none"
                class="hover-btn"
                flat
              >
                <q-tooltip>{{ t('delete') }}</q-tooltip>
              </q-btn>
            </q-td>
            <ContextMenu
              :custom-create="{
                label: t('newBranch'),
                icon: 'subdirectory_arrow_right',
              }"
              :create-fn="() => handleNewBranch(props.node.facultyId)"
              :edit-fn="() => handleEdit(props.node)"
              :delete-fn="
                () => {
                  store.handleRemove({
                    id: props.node.id,
                    node: props.node,
                  });
                }
              "
            ></ContextMenu>
          </q-tr>
        </template>
        <template #default-body="node">
          <q-td v-show="node.node.engName" class="text-body2 q-pl-lg">
            {{ node.node?.abbrev }} | {{ node.node.engName }}
          </q-td>
          <q-separator class="q-mt-md" />
        </template>
      </q-tree>
    </q-scroll-area>
    <!-- pagination -->
    <div class="flex q-my-lg" v-show="store.getMaxPage > 1">
      <q-pagination
        class="q-mx-auto"
        v-model="store.pagination!.page!"
        @update:model-value="store.fetchData()"
        :max="store.getMaxPage"
        direction-links
      />
    </div>
  </q-page>
</template>

<script lang="ts" setup>
import ContextMenu from 'src/components/common/ContextMenu.vue';
import MainHeader from 'src/components/common/PageHeader.vue';
import { useFacultyStore } from 'src/stores/faculty-branch';
import { computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMeta, useQuasar } from 'quasar';
import FacultyDialog from 'src/features/faculty/dialog/FacultyForm.vue';
import BranchDialog from 'src/features/faculty/dialog/BranchForm.vue';
import { nextTick } from 'vue';
import { useRoute } from 'vue-router';
import type { Faculty, Branch } from 'src/types/education';

const route = useRoute();
const title = computed(() => route.matched[1]?.name as string);
const { t } = useI18n();
const store = useFacultyStore();
const $q = useQuasar();

const handleNewFaculty = () => {
  store.resetForm();
  store.titleForm = 'form.New Faculty';
  $q.dialog({
    component: FacultyDialog,
  });
};

const handleEditFaculty = (node: Partial<Faculty>) => {
  store.formFaculty = node;
  store.titleForm = 'form.Edit Faculty';
  $q.dialog({
    component: FacultyDialog,
  });
};

const handleNewBranch = (faculty: Partial<Faculty>) => {
  store.resetForm();
  store.titleForm = 'form.New Branch';
  store.formBranch = { facultyId: faculty['id'] };
  $q.dialog({
    component: BranchDialog,
  });
};

const handleEditBranch = (node: Partial<Branch>) => {
  store.formBranch = node;
  store.titleForm = 'form.Edit Branch';
  $q.dialog({
    component: BranchDialog,
  });
};

const handleEdit = (node: Partial<Branch> | Partial<Faculty>): void => {
  if ('facultyId' in node) {
    handleEditBranch(node);
    return;
  }
  handleEditFaculty(node);
};

onMounted(async () => {
  await nextTick(async () => {
    await store.fetchData();
  });
});

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>

<style lang="scss">
.hover-row:hover {
  color: $secondary;
  background: $surface_light;
}
</style>
