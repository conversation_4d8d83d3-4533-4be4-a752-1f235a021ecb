/**
 * Check if the input text contains Thai characters
 * @param text - The text to check
 * @returns true if the text contains Thai characters, false otherwise
 */
export function isThaiLanguage(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // Thai Unicode range: U+0E00–U+0E7F
  const thaiRegex = /[\u0E00-\u0E7F]/;
  return thaiRegex.test(text);
}

/**
 * Check if the input text is primarily Thai language (more than 50% Thai characters)
 * @param text - The text to check
 * @returns true if more than 50% of characters are Thai, false otherwise
 */
export function isPrimarilyThai(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // Remove whitespace and punctuation for more accurate counting
  const cleanText = text.replace(/[\s\p{P}]/gu, '');
  
  if (cleanText.length === 0) {
    return false;
  }

  // Count Thai characters
  const thaiChars = cleanText.match(/[\u0E00-\u0E7F]/g);
  const thaiCount = thaiChars ? thaiChars.length : 0;
  
  // Check if more than 50% of characters are Thai
  return thaiCount / cleanText.length > 0.5;
}

/**
 * Check if the input text contains only Thai characters (excluding whitespace and punctuation)
 * @param text - The text to check
 * @returns true if all non-whitespace/punctuation characters are Thai, false otherwise
 */
export function isOnlyThai(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // Remove whitespace and punctuation
  const cleanText = text.replace(/[\s\p{P}]/gu, '');
  
  if (cleanText.length === 0) {
    return false;
  }

  // Check if all remaining characters are Thai
  return /^[\u0E00-\u0E7F]+$/.test(cleanText);
}

/**
 * Detect if text contains mixed Thai and English
 * @param text - The text to check
 * @returns object with boolean flags for Thai and English presence
 */
export function detectLanguageMix(text: string): {
  hasThai: boolean;
  hasEnglish: boolean;
  isMixed: boolean;
} {
  if (!text || typeof text !== 'string') {
    return { hasThai: false, hasEnglish: false, isMixed: false };
  }

  const hasThai = /[\u0E00-\u0E7F]/.test(text);
  const hasEnglish = /[a-zA-Z]/.test(text);
  
  return {
    hasThai,
    hasEnglish,
    isMixed: hasThai && hasEnglish
  };
}