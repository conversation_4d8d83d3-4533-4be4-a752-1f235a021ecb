<template>
  <DialogForm
    :title="store.form.id ? t('form.Edit Student') : t('form.New Student')"
    @save="store.handleSubmit"
    :json="store.form"
    ref="formRef"
  >
    <div class="q-gutter-y-sm">
      <FieldBranchOptions
        v-model="store.form.branch"
        v-model:branch-id="store.form.branchId"
        @update:model-value="
          () => {
            formRef.validateForm();
          }
        "
      />
      <q-input
        outlined
        v-model="store.form.thaiName"
        :label="t('name')"
        clearable
        :rules="[requireField]"
      />
      <q-input
        outlined
        v-model="store.form.engName"
        :label="t('engName')"
        clearable
        :rules="[requireField]"
      >
        <template #append></template>
      </q-input>
      <q-input
        label="Date Enrolled"
        readonly
        outlined
        v-model="store.form.dateEnrollment"
      >
        <template v-slot:append>
          <q-icon name="event" class="cursor-pointer">
            <q-popup-proxy
              cover
              transition-show="scale"
              transition-hide="scale"
            >
              <q-date v-model="store.form.dateEnrollment" mask="YYYY-MM-DD">
                <div class="row items-left justify-end">
                  <q-btn v-close-popup label="Close" color="primary" flat />
                </div>
              </q-date>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import { useStudentStore } from 'src/stores/student';
import { requireField } from 'src/utils/field-rules';
import { useI18n } from 'vue-i18n';
import FieldBranchOptions from 'src/components/form/FieldBranchOptions.vue';
import DialogForm from 'src/components/form/DialogForm.vue';
import { ref } from 'vue';

const formRef = ref();
const { t } = useI18n();
const store = useStudentStore();
</script>

<style scoped></style>
