<template>
  <div class="q-gutter-y-md q-mt-md">
    <PageTitle :title="t('subjectManagement')" />
    <PageHeader
      v-model:search-text="filterStore.search"
      @open-dialog="openNewSubjectDialog"
      hide-filter
      hide-search
    />
    <!--Table-->
    <q-table
      flat
      bordered
      class="q-animate--fade q-mt-md"
      separator="cell"
      :rows="store.getListSubjects"
      row-key="code"
      :loading="global.getLoadingState"
      :columns="subjectTabColumns"
      :pagination="store.pagination"
      @update:pagination="store.fetchAllInCurr"
      @request="(p) => store.fetchAllInCurr(p.pagination)"
    >
      <template #body-cell-number="props">
        <q-td>
          {{ props.rowIndex + 1 }}
        </q-td>
      </template>
      <template #body-cell-actions="props">
        <ActionsCell
          @handle-edit="openEditSubjectDialog(props.row)"
          @handle-delete="store.handleDelete(props.row.id)"
        >
          <template #prefix>
            <q-btn
              label="Skill"
              padding="none"
              color="purple-7"
              unelevated
              style="width: 50px"
              @click="openSkillDialog(props.rowIndex)"
            ></q-btn>
            <q-btn
              label="CLOs"
              padding="none"
              color="green-7"
              unelevated
              style="width: 50px"
              @click="openCloDialog(props.rowIndex)"
            ></q-btn>
          </template>
        </ActionsCell>
      </template>
    </q-table>
  </div>
</template>

<script lang="ts" setup>
/*
    imports
*/
import { useGlobalStore } from 'src/stores/global';
import { useI18n } from 'vue-i18n';
import CloDialog from '../../../features/curriculum/dialog/CloDialog.vue';
import SummarySkillDialog from '../../../features/curriculum/dialog/SummarySkillDialog.vue';
import { useSubjectStore } from 'src/stores/subject';
import ActionsCell from 'src/components/table/ActionsCell.vue';
import { useQuasar } from 'quasar';
import SubjectForm from 'src/features/subject/dialog/SubjectForm.vue';
import PageHeader from 'src/components/common/PageHeader.vue';
import PageTitle from 'src/components/common/PageTitle.vue';
import type { Subject } from 'src/types/education';
import useFilterStore from 'src/stores/filter-search';
import { subjectTabColumns } from 'src/data/column_table';
/*
    states
*/
const $q = useQuasar();
const { t } = useI18n();
const global = useGlobalStore();
const store = useSubjectStore();
const filterStore = useFilterStore();

const openNewSubjectDialog = () => {
  store.resetForm();
  store.titleForm = 'form.New Subject';
  $q.dialog({
    component: SubjectForm,
  });
};

const openEditSubjectDialog = (row: Subject) => {
  store.titleForm = 'form.Edit Subject';
  store.form = row;
  $q.dialog({
    component: SubjectForm,
  });
};

const openCloDialog = (index: number) => {
  $q.dialog({
    component: CloDialog,
    componentProps: {
      subject: store.getListSubjects[index],
    },
  });
};
const openSkillDialog = (index: number) => {
  $q.dialog({
    component: SummarySkillDialog,
    componentProps: {
      subject: store.getListSubjects[index],
    },
  });
};
</script>
