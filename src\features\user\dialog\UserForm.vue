<template>
  <DialogForm :title="store.form.id ? t('form.Edit User') : t('form.New User')" @save="store.handleSave">
    <q-input
      v-model="store.form.id"
      label="ID"
      outlined
      readonly
      hint="Readonly"
    />
    <q-input
      type="email"
      outlined
      v-model="store.form.email"
      label="Email *"
      :rules="[requireField]"
    />
    <q-input
      type="password"
      outlined
      v-model="store.form.password"
      label="Password *"
      :rules="[requireField]"
    />
    <q-select
      outlined
      v-model="store.form.role"
      label="Role *"
      :options="Object.values(EnumUserRole)"
      :rules="[requireField]"
    />
  </DialogForm>
</template>

<script setup lang="ts">
import { EnumUserRole } from 'src/data/roles';
import { useUserStore } from 'src/stores/user';
import { requireField } from 'src/utils/field-rules';
import DialogForm from 'src/components/form/DialogForm.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const store = useUserStore();
</script>

<style scoped></style>
