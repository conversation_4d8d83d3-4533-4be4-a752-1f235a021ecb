export function downloadTemplateForScores() {
  const rows = [
    { studentCode: '65160001', gainedLevel: 1 },
    { studentCode: '65160002', gainedLevel: 2 },
    { studentCode: '65160003', gainedLevel: 5 },
  ];
  const csvContent =
    'studentCode,gainedLevel\n' +
    rows.map((r) => `${r.studentCode},${r.gainedLevel}`).join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.setAttribute('download', 'example_import_scores.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export function downloadTemplateForStudents() {
  const rows = [
    {
      studentCode: '65160001',
      thaiName: 'สรุยะ เฉิดฉาย',
      engName: 'suriya cherdchai',
    },
    {
      studentCode: '65160002',
      thaiName: 'จาริยา เจิดจ้า',
      engName: 'jariya jerdjaa',
    },
    {
      studentCode: '65160003',
      thaiName: 'อัศวิน ขุนเขา',
      engName: 'audswin kunkao',
    },
  ];
  const BOM = '\uFEFF';
  const csvContent =
    'studentCode,thaiName,engName\n' +
    rows
      .map((r) => `${r.studentCode},${r.thaiName},${r.engName}`)
      .join('\n');
  const blob = new Blob([BOM + csvContent], {
    type: 'text/csv;charset=utf-8;',
  });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.setAttribute('download', 'example_import_students.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
