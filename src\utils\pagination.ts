import type { QTableProps } from 'quasar';
import _ from 'lodash';
import type { QueryParams } from 'src/types/api';
// default pagination of Quasar
export const defaultPagination = {
  sortBy: 'id',
  descending: false,
  page: 1,
  rowsPerPage: 15,
  rowsNumber: 15, //total
} as QTableProps['pagination'];

export const convertToQuery = (
  pag: QTableProps['pagination'],
  filters?: Partial<QueryParams>,
) => {
  return {
    page: pag?.page,
    limit: pag?.rowsPerPage || 1,
    sort: pag?.sortBy,
    orderBy: pag?.descending ? 'desc' : 'asc',
    ...filters,
  } as Partial<QueryParams>;
};

export const calMaxPage = (
  totalItems: number | undefined,
  rowsPerPage: number | undefined,
): number => {
  const validTotal =
    typeof totalItems === 'number' && totalItems >= 0 ? totalItems : 0;
  const validRowsPerPage =
    typeof rowsPerPage === 'number' && rowsPerPage > 0 ? rowsPerPage : 10;
  return Math.ceil(validTotal / validRowsPerPage);
};
interface DeleteWithPaginationOptions<T> {
  id: number;
  deleteFn: (id: number) => Promise<void> | Promise<unknown>;
  items: T[];
  pagination: QTableProps['pagination'];
  total: number;
  fetchFn: (
    pagination: QTableProps['pagination'],
  ) => Promise<void> | Promise<unknown>;
  setTotal: (newTotal: number) => void;
  setPage: (newPage: number) => void;
}

export async function handleDeleteWithPagination<T>({
  id,
  deleteFn,
  items,
  pagination,
  total,
  fetchFn,
  setTotal,
  setPage,
}: DeleteWithPaginationOptions<T>) {
  const currentPage = pagination?.page || 1;
  const newTotalRows = total - 1;

  await deleteFn(id);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const index = items.findIndex((item: any) => item.id === id);
  if (index !== -1) {
    items.splice(index, 1);
  }

  const remainingRowsOnPage = items.length;
  if (remainingRowsOnPage === 0 && currentPage > 1) {
    setPage(currentPage - 1);
  }

  setTotal(newTotalRows);

  await fetchFn(pagination);
}

export function mergeOrUpdateReactiveArray<T extends { id: number; subs?: T[] }>(
  target: T[],
  incoming: T[],
): T[] {
  // Deep clone incoming to avoid reactive pollution
  const clone = _.cloneDeep(incoming);

  // Create a map of target items by ID
  const targetById = _.keyBy(target, 'id');
  
  // Create a map of incoming items by ID
  const incomingById = _.keyBy(clone, 'id');

  // Merge or add items to create a new array
  const result = target.slice(); // Shallow copy of target to preserve reactivity

  // Update existing items or add new ones
  for (const item of clone) {
    if (targetById[item.id]) {
      // Update existing item by merging
      const index = result.findIndex((t) => t.id === item.id);
      
      // Special handling for nested 'subs' arrays
      if (item.subs && Array.isArray(item.subs)) {
        // If incoming item has subs, recursively merge them
        if (result[index].subs && Array.isArray(result[index].subs)) {
          // If both existing and incoming have subs, recursively merge
          item.subs = mergeOrUpdateReactiveArray(result[index].subs, item.subs);
        }
        // If incoming has subs but existing doesn't, just use incoming subs
      } else if (result[index].subs && !item.subs) {
        // If the existing item has subs but incoming doesn't specify any,
        // keep the existing subs (don't overwrite with undefined)
        item.subs = result[index].subs;
      }
      
      // Now merge the current level item with the recursively updated sub-items
      result[index] = _.merge({}, targetById[item.id], item);
    } else {
      // Add new item
      result.push(item);
    }
  }
  
  // Filter out items that are no longer in the incoming data
  // This handles the case when items are removed on the server
  return result.filter(item => 
    incomingById[item.id] !== undefined || 
    // Keep items that aren't in the incoming page (for pagination)
    !clone.some(incomingItem => incomingItem.id === item.id)
  );
}
