<template>
  <div class="row justify-between">
    <div class="row q-gutter-sm">
      <q-input
        v-if="!hideSearch"
        outlined
        autofocus
        clearable
        v-model="searchText"
        @keyup.enter="$emit('enterSearch')"
        :label="labelSearch ?? t('search')"
        class="col"
        :hint="hintSearch"
        bg-color="white"
        dense
        style="width: 300px"
      >
        <template #prepend>
          <q-icon name="search"></q-icon>
        </template>
      </q-input>
      <FilterButton
        key="filterBtn"
        v-if="!hideFilter"
        v-model="filterModel"
        @confirm-filter="$emit('enterSearch')"
        ref="refFilterBtn"
      />
    </div>
    <div class="col-auto">
      <slot name="top-right"></slot>

      <q-btn
        v-if="!hideAddBtn"
        icon="add"
        key="addBtn"
        @click="$emit('openDialog')"
        color="primary"
        :label="computedLabelAdd"
        style="min-width: 100px"
        unelevated
      >
      </q-btn>
      <q-btn
        v-if="importBtn"
        label="import"
        icon="upload"
        flat
        color="primary"
        @click="$emit('openDialogImport')"
        class="custom-import-btn"
      />
      <q-btn
        v-if="exportBtn"
        label="export"
        icon="cloud_download"
        outline
        @click="$emit('openDialogExport')"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import FilterButton from '../FilterButton.vue';
import type { QueryParams } from 'src/types/api';

const { t } = useI18n();

const props = defineProps<{
  importBtn?: true;
  exportBtn?: true;
  labelSearch?: string;
  hideFilter?: true;
  hideAddBtn?: true;
  labelAdd?: string;
  hideSearch?: true;
  hintSearch?: string;
}>();

const refFilterBtn = ref();

defineExpose({
  refFilterBtn,
});

const emit = defineEmits<{
  (e: 'openDialog'): void;
  (e: 'enterSearch'): void;
  (e: 'openDialogImport'): void;
  (e: 'openDialogExport'): void;
}>();

const searchText = defineModel('searchText', { default: '' });
const filterModel = defineModel<Partial<QueryParams>>('model-filter', {
  default: undefined,
});

const computedLabelAdd = computed(() => props.labelAdd || t('add'));

watch(
  () => filterModel.value,
  (v) => {
    if (Object.keys(v ?? {}).length === 0 || !v) {
      emit('enterSearch');
    }
  },
  { deep: true },
);
</script>

<style scoped lang="scss">
.custom-import-btn {
  border: 2px solid $primary !important;
  color: $primary !important;
  font-weight: 600 !important;
}
</style>
