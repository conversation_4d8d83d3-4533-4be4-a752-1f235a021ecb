import { api } from 'boot/axios';
import type { DataResponse, QueryParams } from 'src/types/api';
import type { Course } from 'src/types/education';

export class CourseService {
  static path = 'courses';

  static async getAll(p?: Partial<QueryParams>) {
    const res = await api.get<DataResponse<Course>>(this.path, { params: p });
    
    return res.data;
  }

  static async getOne(id: string) {
    const res = await api.get(`${this.path}/${id}`);
    return res.data;
  }
  static async getEnrollment(id: number) {
    const res = await api.get(`${this.path}/${id}/enrollments`);
    return res.data;
  }

  static async createMany(obj: object[]) {
    const res = await api.post(this.path, obj);
    return res.data;
  }

  static async updateOne(obj: Course) {
    const res = await api.patch(this.path, obj);
    return res.data;
  }

  static async removeOne(id: number) {
    const res = await api.delete(`${this.path}/${id}`);
    return res.data;
  }
}
