import type { Student } from './education';

export interface ExposeSheetItems<T> {
  dep: Dep;
  __v_isRef: boolean;
  __v_isShallow: boolean;
  _rawValue: T[];
  _value: T[];
}

export interface Dep {
  version: number;
  sc: number;
}

export interface StudentScore {
  id: string | number;
  name: string;
  gainedLevel: number;
  passed?: boolean;
  student: Partial<Student>;
}

export interface SheetStudentScore {
  studentCode: string;
  gainedLevel: number;
}
