import type { User } from './user';

export interface PLO {
  id: number;
  name: string;
  thaiDescription: string;
  engDescription: string;
  type: string;
  curriculumId: number;
  clos?: CLO[];
  curriculum: Curriculum;
}

export type CLO = {
  id: number;
  name: string;
  thaiDescription: string;
  engDescription: string;
  ploId: number;
  plo?: PLO;
  skillId?: number;
  skill?: Skill;
  expectSkillLevel: 1 | 2 | 3 | 4 | 5;
  subjectId?: number;
};

export type Skill = {
  id: number;
  thaiName: string;
  engName: string;
  thaiDescription?: string;
  engDescription?: string;
  domain: LearningDomain;
  parentId?: number;
  parent?: Skill;
  subs?: Skill[];
  curriculum?: Curriculum;
  curriculumId: number;
};

export interface Curriculum {
  id?: number;
  code: string;
  branchId: number;
  thaiName: string;
  engName: string;
  thaiDegree: string;
  engDegree: string;
  thaiDescription: string;
  engDescription: string;
  period: number;
  minimumGrade: string; //decimal point for DTO
  coordinators: Coordinator[];
  plos?: PLO[];
  lessons: Lesson[];
  subjects?: Subject[];
  branch?: Branch;
  skills?: Skill[];
}

export enum EnumSubjectType {
  Compulsory = 'บังคับ',
  Core = 'แกน',
  General = 'ทั่วไป',
  Elective = 'เอกเลือก',
  FreeElective = 'เสรี',
  CWIE = 'CWIE',
}

export const OptionSubjectType = Object.values(EnumSubjectType) as Readonly<
  EnumSubjectType[]
>;

export interface Subject {
  id: number;
  code: string;
  thaiName: string;
  engName: string;
  engDescription: string;
  thaiDescription: string;
  type: EnumSubjectType;
  credit: string;
  lesson?: Lesson;
  curriculumId: number;
  curriculums?: Partial<Curriculum>;
  clos?: CLO[];
  isRoot: boolean;
}

export enum LearningDomain {
  Psychomotor = 'ทักษะ',
  Affective = 'คุณลักษณะบุคคล',
  Cognitive = 'ความรู้',
  Ethics = 'จริยธรรม',
}

export interface Lesson {
  id?: number;
  code: string;
  thaiName: string;
  engName: string;
  curriculum?: Curriculum;
}

export interface Faculty {
  id: number;
  thaiName: string;
  engName: string;
  abbrev: string;
  thaiDescription: string;
  engDescription: string;
  branch: Branch[];
}

export interface Branch {
  id?: number;
  thaiName: string;
  engName: string;
  faculty?: Partial<Faculty>;
  facultyId: number;
  abbrev: string;
  thaiDescription: string;
  engDescription: string;
  curriculums?: Curriculum[];
}

export interface Instructor {
  id?: number;
  email: string;
  thaiName: string;
  engName: string;
  code?: string;
  tel?: string;
  picture?: string;
  officeRoom?: string;
  specialists?: string;
  // socials?: Partial<SocialForm>;
  bio?: string;
  position?: string;
  branch?: Partial<Branch>;
  branchId: number | undefined;
  courses?: Course[];
  curriculums: Partial<Curriculum>[];
  user?: User;
}

export type Coordinator = Instructor;

export interface Student {
  id: number;
  code: string;
  thaiName: string;
  engName: string;
  email?: string;
  dateEnrollment: string;
  skillCollection: SkillCollection[];
  // socials?: Partial<SocialForm>;
  branch?: Partial<Branch> | null;
  branchId: number;
  tel?: string;
}

export interface StudentScore {
  id: string | number;
  name: string;
  gainedLevel: number;
  passed?: boolean;
  student: Partial<Student>;
}

export type Course = {
  id: number;
  active: boolean;
  subjectId: number;
  subject: Partial<Subject>;
  course_instructors?: Partial<Instructor>[];
  semester: number;
  year: number;
};

export interface SkillCollection {
  id?: number;
  course?: Partial<Course>;
  courseId: number;
  studentId: number;
  skillId: number;
  skill?: Partial<Skill>;
  gained: number;
  expected?: number;
}

export interface SkillExpectedLevel {
  id?: number;
  expectedLevel: 1 | 2 | 3 | 4 | 5;
  subject: Partial<Lesson>;
  skill: Partial<Skill>;
  skillCollection: SkillCollection[];
}

export interface SkillCollectionSummary {
  studentId: number;
  studentName: string;
  studentCode: string;
  skills: [{ skillId: number; skillName: string; gainedLevel: number }];
}

export interface SummaryStudent {
  id?: number;
  code: string;
  engName: string;
  enrollmentDate: string;
  thaiName: string;
  curriculumId: string;
  branchId: number;
  userId?: number;
  skill_collections: [
    {
      studentId: number;
      gainedLevel: number;
      cloId: number;
      clo: Partial<CLO>
    }
  ];
}
