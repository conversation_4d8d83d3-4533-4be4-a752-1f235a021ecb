import { defineStore } from 'pinia';
import { Dialog, Notify, type QTableProps } from 'quasar';
import { PloService } from 'src/services/plo';
import { useCurriculumStore } from './curriculum';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import { updateTable } from 'src/utils/table';
import type { QueryParams } from 'src/types/api';
import type { PLO } from 'src/types/education';
import useFilterStore from './filter-search';
import { i18n } from 'src/boot/i18n';

type TitleForm = 'form.New PLO' | 'form.Edit PLO';

export const usePloStore = defineStore('plo', {
  state: () => ({
    dialogState: false,
    form: {} as PLO,
    tabsModel: 'req',
    editMode: true,
    pagination: { ...defaultPagination },
    titleForm: '' as TitleForm,
    listPlo: [] as PLO[],
    curr: useCurriculumStore(),
    filterModel: {} as Partial<QueryParams>,
  }),
  getters: {
    getDialogTitle: (s) => s.titleForm,
    getListPLO: (c) => c.listPlo || [],
  },
  actions: {
    async fetchAllInCurr(
      pag?: QTableProps['pagination'],
      currId?: number | string,
    ) {
      const filterStore = useFilterStore();
      this.filterModel.curriculumId = currId;
      this.filterModel.nameCode = filterStore.getSearch;
      this.pagination = pag || this.pagination;
      const { data } = await PloService.getAll(
        convertToQuery(this.pagination, this.filterModel),
      );
      this.listPlo = data;
    },

    async createOne() {
      const currId = this.curr.getInsertId;
      this.form.curriculumId = currId as number;
      const res = await PloService.createOne(this.form);
      if (res.status === 200) {
        Notify.create({
          type: 'ok',
          message: 'Created successfully',
        });
      }
      this.listPlo = updateTable<PLO>(res.data, this.listPlo, 'create');
    },
    async updateOne() {
      const res = await PloService.updateOne(this.form);
      if (res.status === 200) {
        Notify.create({
          type: 'ok',
          message: 'Updated successfully',
        });
      }
      this.listPlo = updateTable<PLO>(res.data, this.listPlo, 'update');
    },

    async deleteOne(id: number) {
      const res = await PloService.removeOne(id);
      if (res.status === 200) {
        Notify.create({
          type: 'ok',
          message: 'Deleted successfully',
        });
      }
      this.listPlo = updateTable<PLO>(res.data, this.listPlo, 'delete');
    },

    handleDelete(p: PLO) {
      const { t } = i18n.global;

      Dialog.create({
        title: t('dialog.deleteConfirmTitle'),
        message: `${t('dialog.deleteConfirm')} ${p.name}`,
        cancel: true,
        persistent: true,
      })
        .onOk(() => {
          this.deleteOne(p.id)
            .then()
            .catch((err) => {
              console.error('Error deleting PLO:', err);
              Notify.create({
                type: 'negative',
                message: 'Failed to delete PLO. Please try again.',
              });
            });
        })
        .onCancel(() => {});
    },

    async handleSave() {
      if (this.titleForm === 'form.Edit PLO') {
        await this.updateOne();
      } else {
        await this.createOne();
      }
      this.dialogState = false;
    },

    handleEdit(form: PLO) {
      this.form = { ...form };
      this.titleForm = 'form.Edit PLO';
      this.toggleDialog();
    },

    handleCreate() {
      this.form = {} as PLO;
      this.titleForm = 'form.New PLO';
      this.toggleDialog();
    },

    toggleDialog() {
      this.dialogState = !this.dialogState;
    },

    resetForm() {
      this.form = {} as PLO;
    },
  },
});
