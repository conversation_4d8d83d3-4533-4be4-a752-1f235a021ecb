<template>
  <div class="">
    <PageTitle :title="t('curriculumManagement')" />
    <form class="q-mt-md" v-if="!loading">
      <q-scroll-area :visible="true" style="height: calc(100vh - 260px)">
        <div class="q-gutter-y-sm q-pr-lg">
          <FieldBranchOptions v-model:branch-id="store.form.branchId" />
          <q-input
            outlined
            v-model="store.form.code"
            :label="t('curriculumCode')"
            :rules="[requireField, ruleCodeCurrFormat]"
            maxlength="14"
          >
          </q-input>
          <q-input
            outlined
            v-model="store.form.thaiName"
            :label="t('name')"
            :rules="[requireField]"
          />
          <q-input
            outlined
            v-model="store.form.engName"
            :label="t('engName')"
            :rules="[requireField]"
          />
          <q-input
            type="textarea"
            outlined
            v-model="store.form.thaiDescription"
            :label="t('description')"
            :rules="[requireField]"
            counter
            maxlength="1000"
          />
          <q-input
            type="textarea"
            outlined
            v-model="store.form.engDescription"
            :label="t('englishDescription')"
            :rules="[requireField]"
            counter
            maxlength="1000"
          />
          <q-select
            outlined
            :options="OptionEducationLevelTH"
            v-model="store.form.thaiDegree"
            :label="t('degree')"
            :rules="[requireField]"
          />
          <q-select
            outlined
            v-model="store.form.engDegree"
            :options="OptionEducationLevelEN"
            :label="t('engDegree')"
            :rules="[requireField]"
          />
          <q-input
            type="number"
            outlined
            v-model.number="store.form.period"
            :label="t('period')"
            :rules="[requireField]"
          >
          </q-input>
          <q-input
            outlined
            mask="#.##"
            v-model="store.form.minimumGrade"
            :label="t('minimumGrade')"
            :rules="[requireField]"
          />
        </div>
      </q-scroll-area>
      <div class="row col-12">
        <q-btn
          :label="t('save')"
          color="primary"
          unelevated
          class="col-grow"
          @click="store.handleSave"
        />
      </div>
    </form>
    <q-scroll-area
      class="q-px-lg"
      v-else
      :visible="true"
      style="height: calc(100vh - 250px)"
    >
      <q-skeleton
        v-for="i in 8"
        :key="i"
        class="q-mt-lg"
        width="100%"
        height="40px"
        type="QInput"
      />
    </q-scroll-area>
  </div>
</template>

<script lang="ts" setup>
import { requireField, ruleCodeCurrFormat } from 'src/utils/field-rules';
import { ref, watch } from 'vue';
import { useCurriculumStore } from 'src/stores/curriculum';
import { useI18n } from 'vue-i18n';
import {
  OptionEducationLevelEN,
  OptionEducationLevelTH,
} from 'src/data/education_level';
import FieldBranchOptions from 'src/components/form/FieldBranchOptions.vue';
import PageTitle from 'src/components/common/PageTitle.vue';

const store = useCurriculumStore();
const { t } = useI18n();
const loading = ref(false);
watch(
  () => [store.form?.thaiDegree, store.form?.engDegree],
  ([newThaiDegree, newEngDegree], [oldThaiDegree, oldEngDegree]) => {
    // Avoid infinite loops by checking if the value has actually changed
    if (newThaiDegree !== oldThaiDegree) {
      const indexTH = OptionEducationLevelTH.indexOf(newThaiDegree);
      if (indexTH !== -1) {
        store.form.engDegree = OptionEducationLevelEN[indexTH]!;
      }
    } else if (newEngDegree !== oldEngDegree) {
      const indexEN = OptionEducationLevelEN.indexOf(newEngDegree);
      if (indexEN !== -1) {
        store.form.thaiDegree = OptionEducationLevelTH[indexEN]!;
      }
    }
  },
  { deep: true },
);
</script>
