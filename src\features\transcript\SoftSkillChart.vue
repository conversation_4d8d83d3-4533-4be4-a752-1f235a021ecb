<template>
  <div ref="chartContainer" style="width: 400px; height: 400px"></div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onBeforeUnmount,
  computed,
} from 'vue';
import * as d3 from 'd3';
import { useRoute } from 'vue-router';
import { SkillCollectionService } from 'src/services/skill-collections';

interface DataPoint {
  axis: string;
  value: number;
}

interface skill {
  id: string;
  name: string;
  domain: string;
  gained: number;
  subskills: skill[];
}

interface SkillResponse {
  soft: skill[];
  specific: skill[];
}

export default defineComponent({
  name: 'RadarChart',
  setup() {
    const chartContainer = ref<HTMLElement | null>(null);
    const data = ref<SkillResponse | null>(null);
    const route = useRoute();
    const studentCode = computed(() => String(route.params.code));

    onMounted(async () => {
      data.value = await SkillCollectionService.fetchTranscript(
        studentCode.value,
      );

      const deduplicatedSoftMap = new Map<string, skill>();
      data.value?.soft.forEach((s) => {
        const existing = deduplicatedSoftMap.get(s.name);
        if (!existing || s.gained > existing.gained) {
          deduplicatedSoftMap.set(s.name, s);
        }
      });

      const softSkills: DataPoint[] = (() => {
        const deduped = Array.from(deduplicatedSoftMap.values())
          .map((d) => ({ axis: d.name, value: d.gained }))
          .sort((a, b) => b.value - a.value);

        if (deduped.length >= 3) {
          return deduped.slice(0, 5);
        } else {
          const maxScore = deduped[0]?.value ?? 1;
          const dummyValue = maxScore * 0.1;
          const filled = [...deduped];
          const dummyCount = 5 - filled.length;
          for (let i = 0; i < dummyCount; i++) {
            filled.push({ axis: ``, value: dummyValue });
          }
          return filled;
        }
      })();

      if (chartContainer.value) {
        drawRadarChart(chartContainer.value, softSkills);
      }
    });

    onBeforeUnmount(() => {
      d3.select(chartContainer.value).selectAll('*').remove();
    });

    return { chartContainer };
  },
});

function drawRadarChart(container: HTMLElement, data: DataPoint[]) {
  const width = 400;
  const height = 400;
  const radius = Math.min(width, height) / 2 - 60;
  const angleSlice = (Math.PI * 2) / data.length;

  // ✅ Map score to level (0–3)
  function scoreToLevel(score: number): number {
    if (score >= 3.5) return 3;
    if (score >= 2) return 2;
    if (score >= 0.5) return 1;
    return 0;
  }

  const levelData = data.map((d) => ({
    axis: d.axis,
    value: scoreToLevel(d.value),
    rawScore: d.value,
  }));

  const rScale = d3.scaleLinear().domain([0, 3]).range([0, radius]);

  const svg = d3
    .select(container)
    .append('svg')
    .attr('width', width)
    .attr('height', height)
    .append('g')
    .attr('transform', `translate(${width / 2}, ${height / 2})`);

  // ✅ Draw levels 0–3
  svg
    .selectAll('.levels')
    .data(d3.range(0, 4)) // [0, 1, 2, 3]
    .enter()
    .append('g')
    .each(function (d) {
      d3.select(this)
        .append('circle')
        .attr('r', rScale(d))
        .style('fill', '#CDCDCD')
        .style('stroke', '#aaa')
        .style('fill-opacity', d === 0 ? 0 : 0.1);

      d3.select(this)
        .append('text')
        .attr('x', d === 0 ? 0 : 6)
        .attr('y', -rScale(d))
        .attr('text-anchor', d === 0 ? 'middle' : 'start')
        .attr('dy', '0.35em')
        .style('font-size', '10px')
        .style('fill', '#666')
        .text(d === 0 ? '' : `L ${d}`);
    });

  const axis = svg
    .selectAll('.axis')
    .data(levelData)
    .enter()
    .append('g')
    .attr('class', 'axis');

  axis
    .append('line')
    .attr('x1', 0)
    .attr('y1', 0)
    .attr('x2', (d, i) => rScale(3) * Math.cos(angleSlice * i - Math.PI / 2))
    .attr('y2', (d, i) => rScale(3) * Math.sin(angleSlice * i - Math.PI / 2))
    .attr('stroke', '#CDCDCD')
    .attr('stroke-width', '1px');

  axis
    .append('text')
    .attr('x', (d, i) => rScale(3.4) * Math.cos(angleSlice * i - Math.PI / 2))
    .attr('y', (d, i) => rScale(3.4) * Math.sin(angleSlice * i - Math.PI / 2))
    .attr('text-anchor', 'middle')
    .attr('font-size', '11px')
    .text((d) => d.axis);

  const radarLine = d3
    .lineRadial<{ axis: string; value: number }>()
    .radius((d) => rScale(d.value))
    .angle((_, i) => i * angleSlice)
    .curve(d3.curveLinearClosed);

  svg
    .append('path')
    .datum(levelData)
    .attr('d', radarLine)
    .attr('fill', '#28a745')
    .attr('fill-opacity', 0)
    .transition()
    .duration(1200)
    .attr('fill-opacity', 0.4);

  const filteredData = levelData
    .map((d, i) => ({ ...d, index: i }))
    .filter((d) => d.axis !== '');

  svg
    .selectAll('.radarCircle')
    .data(filteredData)
    .enter()
    .append('circle')
    .attr('r', 0)
    .attr(
      'cx',
      (d) => rScale(d.value) * Math.cos(angleSlice * d.index - Math.PI / 2),
    )
    .attr(
      'cy',
      (d) => rScale(d.value) * Math.sin(angleSlice * d.index - Math.PI / 2),
    )
    .attr('fill', '#28a745')
    .transition()
    .delay((_, i) => i * 100)
    .duration(1000)
    .attr('r', 4);
}
</script>

<style scoped>
.axis text {
  font-family: Arial, sans-serif;
  fill: #666;
}
</style>
