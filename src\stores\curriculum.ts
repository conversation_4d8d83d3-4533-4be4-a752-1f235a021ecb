import { defineStore } from 'pinia';
import { Dialog, Notify } from 'quasar';
import { CurriculumService } from 'src/services/curriculums';
import type { QueryParams } from 'src/types/api';
import type { Curriculum } from 'src/types/education';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import useFilterStore from './filter-search';
type TitleForm = 'form.New Curriculum' | 'form.Edit Curriculum';

export const useCurriculumStore = defineStore('curriculum', {
  state: () => ({
    form: {} as Curriculum,
    curriculums: <Curriculum[]>[],
    curriculumsOptions: <Curriculum[]>[],
    pagination: { ...defaultPagination },
    dialogState: false,
    titleForm: '' as TitleForm,
    filterModel: {} as Partial<QueryParams>,
    foundExistCurriculum: false,
    codeLabeL: '',
  }),
  getters: {
    getForm: (c) => c.form,
    getCurriculumCodeLabel: (c) => c.codeLabeL,
    getCurriculums: (c) => c.curriculums,
    getDialogTitle: (c) => c.titleForm,
    getInsertId: (c) => c.form.id,
    getCode: (c) => c.form.code || '',
    getSkills: (c) => {
      return c.form.skills?.map((skill, index) => ({ ...skill, index })) || [];
    },
    getCurriculum: (c) => c.form,
    getListSubject: (c) => c.form.subjects?.flatMap((c) => c.lesson) || [],
    getListCourseSpec: (c) => c.form.subjects,
    getBranch: (c) => c.form.branch,
  },
  actions: {
    async fetchOneByCode(code: string): Promise<void> {
      try {
        const response = await CurriculumService.getOneByCode(code);
        this.form = response;
      } catch (error) {
        console.error('Error fetching curriculum by code:', error);
        throw error;
      }
    },
    async fetchAll() {
      try {
        const filterStore = useFilterStore();
        this.filterModel.nameCode = filterStore.getSearch;
        const { data, total } = await CurriculumService.getAll(
          convertToQuery(this.pagination, this.filterModel),
        );
        this.curriculums = JSON.parse(JSON.stringify(data));
        this.pagination.rowsNumber = total || 0;
      } catch (error) {
        console.error('❌', error);
      }
    },
    toggleDialogForm(form?: Curriculum) {
      if (form) {
        this.titleForm = 'form.Edit Curriculum';
        this.form = { ...form };
      } else {
        this.titleForm = 'form.New Curriculum';
        this.resetForm();
      }
      this.dialogState = !this.dialogState;
    },
    async handleCreateOne() {
      const ok = await CurriculumService.createOne(this.form);
      if (ok) {
        Notify.create({
          type: 'ok',
          message: 'Curriculum created successfully',
        });
        this.dialogState = false;
        this.form = {} as Curriculum;
        await this.fetchAll();
      }
    },

    async handleSave() {
      const ok = await CurriculumService.updateOne(this.form);
      if (ok) {
        Notify.create({
          type: 'ok',
          message: 'Curriculum saved successfully',
        });
        this.dialogState = false;
        await this.router.replace(`/curriculums/${this.form.code}`);
      }
    },
    handleDelete(id: number) {
      Dialog.create({
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this curriculum?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        CurriculumService.removeOne(id)
          .then(async (ok) => {
            if (ok) {
              Notify.create({
                type: 'ok',
                message: 'Curriculum removed successfully',
              });
              await this.fetchAll();
            }
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },

    async checkUpdateCode(val: string | number | null) {
      if (typeof val === 'string') {
        if (val.length === 14) {
          const exist = await CurriculumService.getOneByCode(this.getCode);
          if (exist) {
            this.codeLabeL = 'This code already exists';
            this.foundExistCurriculum = true;
          } else {
            this.codeLabeL = 'Available code';
            this.foundExistCurriculum = false;
          }
        }
      }
    },
    resetForm() {
      this.form = {} as Curriculum;
    },
  },
});
