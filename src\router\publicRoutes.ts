import type { RouteRecordRaw } from 'vue-router';

export const publicRoutes: RouteRecordRaw[] = [
  {
    path: '/landing',
    component: () => import('src/layouts/FullLayout.vue'),
    meta: { public: true },
    children: [
      {
        path: '',
        name: 'Landing Page',
        component: () => import('src/pages/public/LandingPage.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('layouts/FullLayout.vue'),
    meta: { public: true },
    children: [
      {
        path: '',
        name: 'Login',
        component: () => import('src/pages/public/LoginPage.vue'),
      },
    ],
  },
  {
    path: '/transcript',
    component: () => import('layouts/FullLayout.vue'),
    meta: { public: true },
    children: [
      {
        path: ':code',
        name: 'Student Transcript',
        component: () => import('src/pages/public/TranscriptPage.vue'),
      },
    ],
  },
  {
    path: '/evaluation',
    component: () => import('layouts/FullLayout.vue'),
    meta: { public: true },
    children: [
      {
        path: ':code',
        name: 'Student Evaluation',
        component: () => import('src/pages/public/EvaluationPage.vue'),
      },
    ],
  },
  {
    path: '/about',
    component: () => import('src/layouts/FullLayout.vue'),
    meta: { public: true },
    children: [
      {
        path: '',
        name: 'About',
        component: () => import('src/pages/public/AboutPage.vue'),
      },
    ],
  },
];
