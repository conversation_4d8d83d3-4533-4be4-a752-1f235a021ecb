<template>
  <q-page>
    <!-- Show tabs and chart only when not on student list page -->
    <div v-if="!isStudentListRoute">
      <q-tabs v-model="tab" dense align="justify" no-caps narrow-indicator>
        <q-tab name="hard" label="Hard Skills" />
        <q-tab name="soft" label="Soft Skills" />
      </q-tabs>
      <q-separator />
      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="hard" v-show="tab === 'hard'">
          <SummarySkillChart :skillType="tab" :key="'hard'"></SummarySkillChart>
        </q-tab-panel>
        <q-tab-panel name="soft" v-show="tab === 'soft'">
          <SummarySkillChart :skillType="tab" :key="'soft'"></SummarySkillChart>
        </q-tab-panel>
      </q-tab-panels>
    </div>

    <!-- Router outlet for child routes like SummaryStudentList -->
    <router-view v-if="isStudentListRoute" />
  </q-page>
</template>

<script setup lang="ts">
import { useCurriculumStore } from 'src/stores/curriculum';
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import SummarySkillChart from '../summary/SummarySkillChart.vue';

const route = useRoute();
const router = useRouter();
const currentYear = new Date().getFullYear() + 543;
const lastTwoDigits = currentYear.toString().slice(-2);
const skillParam = route.query.skillType;
const yearParam = ref(route.query.year as string || lastTwoDigits);

const tab = ref(skillParam === 'soft' ? 'soft' : 'hard');

// Check if current route is the student list route
const isStudentListRoute = computed(() => {
  return route.name === 'SummaryStudentList';
});

// Watch for route parameter changes
watch(
  () => route.query.skillType,
  (newSkillType) => {
    if (newSkillType && (newSkillType === 'soft' || newSkillType === 'hard')) {
      tab.value = newSkillType;
    }
  }
);

// Watch for year parameter changes
watch(
  () => route.query.year,
  (newYear) => {
    if (newYear && typeof newYear === 'string') {
      yearParam.value = newYear;
    }
  },
  { immediate: true } // Ensure it runs immediately to sync with URL
);

// Watch for tab changes and update URL
watch(
  () => tab.value,
  async (val) => {
    // Only update URL if skillType is different from current query
    if (route.query.skillType !== val) {
      await router.replace({
        path: route.path,
        query: {
          ...route.query, // Preserve all existing query parameters
          skillType: val
        }
      });
    }
  }
);
const store = useCurriculumStore();
const currCode = computed(() => route.params.code as string);

onMounted(async () => {
  if (!store.getInsertId) {
    await store.fetchOneByCode(currCode.value);
  }
  

  if (route.query.year && route.params.skillType) {
    yearParam.value = route.query.year as string;
  } else if (lastTwoDigits) {
    // Only set default year if no year is present in URL
    await router.replace({
      path: route.path,
      query: {
        ...route.query,
        year: lastTwoDigits,
        skillType: 'hard'
      }
    });
    yearParam.value = lastTwoDigits;
  }
});
</script>

<style scoped>
.sum-card {
  width: 100%;
  min-width: 400px;
  min-height: 300px;
  max-height: 300px;
  overflow: hidden;
}

.panel-active {
  background-color: #ffffff;
  /* หรือสีอ่อนๆเช่น #f9f9f9 */
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
</style>
