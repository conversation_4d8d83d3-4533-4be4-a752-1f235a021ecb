<template>
  <q-card flat bordered>
    <q-expansion-item
      dense
      style="overflow: hidden"
      label="View JSON"
      v-model="expanded"
      class="text-grey-8 text-caption"
      :duration="0"
    >
      <div class="flex justify-end q-pa-sm">
        <q-btn flat padding="xs" @click="copyToClipboard">
          <q-icon size="xs" :name="alreadyCopied ? 'check' : matContentCopy"></q-icon>
          <q-tooltip> {{ alreadyCopied ? 'Copied' : 'Copy' }}</q-tooltip>
        </q-btn>
      </div>
      <vue-json-pretty :data="$props.data" class="q-pa-md bg-grey-1" />
    </q-expansion-item>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import VueJsonPretty from 'vue-json-pretty';
import type { JSONDataType } from 'vue-json-pretty/types/utils';
import { matContentCopy } from '@quasar/extras/material-icons';

const alreadyCopied = ref(false);
const expanded = ref<boolean>(false);

const copyToClipboard = async () => {
  const prettyString = JSON.stringify(props.data, undefined, 2);
  await navigator.clipboard.writeText(prettyString);
  alreadyCopied.value = true;
};

const props = defineProps<{
  data: JSONDataType;
}>();
</script>
