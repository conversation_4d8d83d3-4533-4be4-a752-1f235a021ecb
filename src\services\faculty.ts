import { api } from 'src/boot/axios';
import type { QueryParams } from 'src/types/api';
import type { Faculty } from 'src/types/education';

export class FacultyService {
  static path = 'faculties';

  static async getAll(p?: Partial<QueryParams>) {
    const res = await api.get(this.path, { params: p });
    return res.data;
  }

  static async getOne(id: string) {
    const res = await api.get(`${this.path}/${id}`);
    return res.data;
  }

  static async createOne(obj: Partial<Faculty>) {
    delete obj.branch;
    const res = await api.post(this.path, obj);
    return res.data;
  }

  static async updateOne(obj: Partial<Faculty>) {
    delete obj.branch;
    const res = await api.patch(`${this.path}/${obj.id}`, obj);
    return res.data;
  }

  static async removeOne(id: string) {
    const res = await api.delete(`${this.path}/${id}`);
    return res.data;
  }
}
