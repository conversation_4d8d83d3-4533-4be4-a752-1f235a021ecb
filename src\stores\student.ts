import { defineStore } from 'pinia';
import { Dialog, Notify, type QTableProps } from 'quasar';
import { StudentService } from 'src/services/student';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import { updateTable } from 'src/utils/table';
import type { Student } from 'src/types/education';
import useFilterStore from './filter-search';

interface StudentState {
  dialog: {
    state: boolean;
    title: 'form.Edit Student' | 'form.Add Student';
  };
  form: Student;
  students: Student[];
  pagination: typeof defaultPagination;
}

export const useStudentStore = defineStore('student', {
  state: (): StudentState => ({
    dialog: {
      state: false,
      title: 'form.Edit Student',
    },
    form: {} as Student,
    students: [],
    pagination: { ...defaultPagination },
  }),

  getters: {
    getStudents: (state): Student[] => state.students,
    getTotal: (state): number => state.pagination?.rowsNumber || 0,
    getMaxPage: (state): number =>
      Math.ceil(state.pagination!.rowsNumber! / state.pagination!.rowsPerPage!),
    getForm: (state): Student => state.form,
    getDialogState: (state): boolean => state.dialog.state,
    getDialogTitle: (state): string => state.dialog.title,
  },

  actions: {
    async fetchAll(pagination?: QTableProps['pagination']) {
      try {
        this.pagination = pagination || this.pagination;
        const filterStore = useFilterStore();
        const filter = convertToQuery(this.pagination, filterStore.getQuery);

        const { data, total } = await StudentService.getAll(filter);
        this.students = data;
        this.pagination!.rowsNumber = total;
      } catch (error) {
        throw new Error(`Failed to fetch students: ${String(error)}`);
      }
    },

    toggleDialog(options?: {
      form?: Student;
      title?: StudentState['dialog']['title'];
    }) {
      this.dialog.state = !this.dialog.state;
      if (options?.title) {
        this.dialog.title = options.title;
      } else {
        // Set default title based on whether we're editing or adding
        this.dialog.title = options?.form ? 'form.Edit Student' : 'form.Add Student';
      }
      if (options?.form) {
        this.form = { ...options.form };
      } else {
        this.form = {} as Student;
      }
    },

    resetState() {
      this.students = [];
      this.form = {} as Student;
      this.pagination = { ...defaultPagination };
    },

    async handleSubmit() {
      try {
        if (this.form.id) {
          const updatedStudent = await StudentService.updateOne(this.form);
          // Update specific row using updateTable
          this.students = updateTable(updatedStudent, this.students, 'update');
          this.toggleDialog();
          Notify.create({
            type: 'positive',
            message: 'Updated successfully',
          });
        } else {
          const newStudent = await StudentService.createOne(this.form);
          // Add new row using updateTable
          this.students = updateTable(newStudent, this.students, 'create');
          this.toggleDialog();
          Notify.create({
            type: 'positive',
            message: 'Created successfully',
          });
        }
      } catch (error) {
        Notify.create({
          type: 'negative',
          message: `Failed to submit: ${String(error)}`,
        });
      }
    },

    async handleRemove(student: Student) {
      try {
        // Create dialog and handle confirmation with promise
        await new Promise<void>((resolve, reject) => {
          Dialog.create({
            title: 'Confirm Deletion',
            message: 'Are you sure you want to delete this student?',
            persistent: true,
            ok: {
              label: 'Delete',
              color: 'negative',
            },
            cancel: {
              label: 'Cancel',
              flat: true,
            },
          })
            .onOk(() => {
              StudentService.removeOne(student.id)
                .then(() => {
                  // Use updateTable to remove specific row
                  this.students = updateTable(student, this.students, 'delete');

                  Notify.create({
                    type: 'positive',
                    message: 'Deleted successfully',
                  });
                  resolve();
                })
                .catch((err) => {
                  // Convert to Error object if it's not already one
                  const error =
                    err instanceof Error ? err : new Error(String(err));
                  reject(error);
                });
            })
            .onCancel(() => {
              resolve();
            });
        });
      } catch (error) {
        Notify.create({
          type: 'negative',
          message: `Failed to delete: ${String(error)}`,
        });
      }
    },
  },
});
