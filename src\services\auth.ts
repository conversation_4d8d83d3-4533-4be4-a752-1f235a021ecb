import { AxiosError, type AxiosResponse } from 'axios';
import { api } from 'boot/axios';
import { LocalStorage, Notify } from 'quasar';
import { i18n } from 'src/boot/i18n';
import type { AuthPayload } from 'src/types/api';

class AuthService {
  static async login(email: string, password: string) {
    try {
      return await api.post<AuthPayload>('/auth/login', {
        email,
        password,
      });
    } catch (error) {
      if (error instanceof AxiosError) {
        const { t } = i18n.global;
        if (
          error.response?.status === 401 &&
          error.response.data.message === 'Invalid credentials'
        ) {
          LocalStorage.remove('userPayload');
          Notify.create({
            type: 'negative',
            message: t('invalidEmailOrPassword'),
            progress: true,
          });
        }
      }
    }
  }

  static loginGoogle() {
    localStorage.removeItem('token');
    window.location.href = api.defaults.baseURL + '/auth/google';
  }

  static async logout(userId: number): Promise<AxiosResponse> {
    try {
      const res = await api.get(`/auth/logout/${userId}`, {
        withCredentials: true,
      });
      LocalStorage.removeItem('userPayload');
      return res.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  static async fetchProfile(): Promise<AuthPayload | null> {
    try {
      const { data } = await api.get<AuthPayload>('auth/profile');
      if (!data) {
        return null;
      }
      return data;
    } catch (err) {
      console.error(err);
      return null;
    }
  }
}
export default AuthService;
