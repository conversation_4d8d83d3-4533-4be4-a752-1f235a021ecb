<template>
  <q-dialog v-model="dialogState" :title="t('updateScore')">
    <q-card style="min-width: 80%" class="q-pa-md">
      <q-card-section class="q-pb-none">
        <div class="row justify-between">
          <div class="text-h6 text-primary">{{ t('updateScore') }}</div>
          <q-btn
            fab-mini
            flat
            padding="none"
            icon="close"
            @click="() => (dialogState = false)"
          ></q-btn>
        </div>

        <q-separator
          class="q-my-sm bg-primary"
          style="height: 3px"
        ></q-separator>
      </q-card-section>
      <q-table :rows="store.getStudentsScores" :columns="columnsScores" flat>
        <template #top-left>
          <div>
            <div class="highlight-1 text-h6">
              {{ store.selectedClo.name }}
            </div>
            <div class="text-body">
              <div>
                <span class="highlight-1">{{ t('thaiDescription') }}</span>
                : {{ store.selectedClo.thaiDescription }}
              </div>
              <div>
                <span class="highlight-1">{{ t('skills') }}</span> :
                {{ store.selectedClo.skill?.thaiName }}
              </div>
              <div>
                <span class="highlight-1">{{ t('expectedLevel') }} :</span>
                {{ store.selectedClo.expectSkillLevel || 'ไม่มีข้อมูล' }}
              </div>
              <div>
                <span class="highlight-1">{{ t('domain') }} :</span>
                {{ store.selectedClo.skill?.domain || 'ไม่มีข้อมูล' }}
              </div>
            </div>
          </div>
        </template>
        <template #top-right>
          <div class="q-gutter-x-sm row">
            <q-input :label="t('search')" v-model="search" outlined dense>
              <template #prepend>
                <q-icon name="search" />
              </template>
            </q-input>
            <q-btn
              @click="toggleDialogImport()"
              color="primary"
              unelevated
              :label="t('upload')"
            />
            <q-dialog
              v-model="dialogUpload"
              transition-hide="fade"
              transition-show="fade"
              transition-duration="100"
            >
              <q-card style="min-width: 60%">
                <q-card-section class="q-pb-none">
                  <div class="row justify-between">
                    <div class="text-h6 text-weight-medium text-primary">
                      {{ t('upload') }}
                    </div>
                    <q-btn
                      fab-mini
                      flat
                      padding="none"
                      icon="close"
                      @click="dialogUpload = false"
                    />
                  </div>
                  <q-separator class="q-my-sm bg-primary" style="height: 3px" />
                </q-card-section>
                <q-card-section>
                  <TableSheetJS
                    :valid="true"
                    ref="tableSheetJSRef"
                    @download-template="downloadTemplateForScores"
                    @confirm="setStudentScores"
                  />
                </q-card-section>
              </q-card>
            </q-dialog>
          </div>
        </template>
        <template #body-cell-number="props">
          <q-td>{{ props.rowIndex + 1 }}</q-td>
        </template>
        <template #body-cell-gainedLevel="props">
          <q-td>
            <q-input
              class="rounded-borders score-field"
              :input-style="{
                // color: props.row[props.col.name] > 0 ? 'green' : 'red',
              }"
              outlined
              v-model.number="props.row[props.col.name]"
              type="number"
              min="0"
              :max="
                store.selectedClo.skill?.domain === LearningDomain.Affective ||
                store.selectedClo.skill?.domain === LearningDomain.Ethics
                  ? 3
                  : 5
              "
              :rules="createScoreRule(store.selectedClo.skill!.domain)"
              dense
            />
          </q-td>
        </template>
      </q-table>
      <q-card-actions align="right">
        <q-btn color="primary" class="fit" unelevated @click="saveScore">
          {{ t('save') }}
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import TableSheetJS from 'src/components/form/TableSheetJS.vue';
import { downloadTemplateForScores } from 'src/utils/file-template';
import { SkillCollectionService } from 'src/services/skill-collections';
import { onMounted, ref } from 'vue';
import type { QTableColumn } from 'quasar';
import { useCourseStore } from 'src/stores/course';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

import type { SheetStudentScore } from 'src/types/sheetjs';
import { LearningDomain } from 'src/types/education';

const search = ref('');
const dialogState = ref();
const { t } = useI18n();
const route = useRoute();
const store = useCourseStore();
const dialogUpload = ref(false);
const columnsScores: QTableColumn[] = [
  {
    name: 'number',
    label: t('no.'),
    field: '',
    align: 'left',
  },
  {
    name: 'code',
    label: t('code'),
    field: (r) => r.student.code,
    align: 'left',
  },
  {
    name: 'gainedLevel',
    label: t('gainedLevel'),
    field: (r) => r.gainedLevel,
    align: 'left',
  },
];

const toggleDialogImport = () => {
  dialogUpload.value = !dialogUpload.value;
};
const setStudentScores = async (v: unknown) => {
  try {
    const sc = ref<SheetStudentScore[]>([]);
    sc.value = JSON.parse(JSON.stringify(v));

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const payload: any[] = [];
    for (let i = 0; i < sc.value.length; i++) {
      const domain = store.selectedClo.skill?.domain;
      if (
        (domain === LearningDomain.Ethics ||
          domain === LearningDomain.Affective) &&
        sc.value[i].gainedLevel > 3
      ) {
        // window alert('ไม่สามารถใส่คะแนนมากกว่า 3 ได้');
        alert(
          'Skill Domain จริยธรรม และ ลักษณะบุคคล ไม่สามารถใส่คะแนนมากกว่า 3 ได้',
        );
        return;
      }
      payload.push({
        studentCode: sc.value[i].studentCode.toString(),
        gainedLevel: sc.value[i].gainedLevel,
      });
    }

    await SkillCollectionService.postImportScores(
      route.params.id as string,
      String(store.getSelectedCloId),
      payload,
    );
    await store.fetchStudentScores(
      route.params.id as string,
      store.getSelectedCloId,
    );
  } catch (error) {
    console.error('❌ failed to parse sheet items', error);
  }
  dialogUpload.value = false;
};

const saveScore = async () => {
  try {
    const payload = store.getStudentsScores.map((s) => ({
      studentCode: s.student.code ?? '',
      gainedLevel: s.gainedLevel,
    }));

    await SkillCollectionService.postImportScores(
      route.params.id as string,
      String(store.getSelectedCloId),
      payload,
    );
    await store.fetchCourseId(route.params.id as string);
    dialogState.value = false;
  } catch (error) {
    console.error('❌ save error:', error);
  }
};

function createScoreRule(domain: LearningDomain) {
  const maxScore =
    domain === LearningDomain.Ethics || domain === LearningDomain.Affective
      ? 3
      : 5;
  return [
    (v: number) => {
      if (v === null || v === undefined) {
        return 'Value is required';
      }
      if (v >= 0 && v <= maxScore) {
        return true; // Valid
      }

      return `Incorrect value (1-${maxScore})`;
    },
  ];
}

onMounted(async () => {
  await store.fetchStudentScores(
    route.params.id as string,
    store.getSelectedCloId,
  );
});
</script>

<style scoped lang="scss">
.score-field {
  width: 100px;
}
</style>
