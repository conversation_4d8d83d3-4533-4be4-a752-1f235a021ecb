<template>
  <q-page class="q-pa-md background-pattern">
    <div
      class="header-container-line"
      style="
        padding: 20px;
        margin-top: -16px;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
      "
    ></div>
    <div class="header-bg">
      <div class="header-container">
        <div class="logo-container">
          <q-img
            src="logos/buu-og.png"
            alt="logo"
            height="160px"
            width="160px"
            fit="contain"
            class="buu-logo"
          />
        </div>
        <div class="title-container">
          <h2 class="title">SKILL TRANSCRIPT</h2>
          <h6 class="subtitle">
            วิทยาศาสตรบัณฑิต {{ branch?.thaiName || '่ไม่มีข้อมูล' }}<br />
            Bachelor of Science {{ branch?.engName || '่ไม่มีข้อมูล' }}
          </h6>
          <h5 class="faculty">คณะวิทยาการสารสนเทศ | Faculty of Informatics</h5>
          <h5 class="faculty" style="margin-top: -20px">
            มหาวิทยาลัยบูรพา | Burapha University
          </h5>
        </div>
        <div class="student-photo-wrapper">
          <q-img
            class="graduate-photo"
            :src="`https://reg.buu.ac.th/registrar/getstudentimage.asp?id=${studentCode}`"
            alt="Graduate"
            height="220px"
            width="170px"
            fit="cover"
          >
            <template #loading></template>
          </q-img>
        </div>
      </div>
    </div>

    <div
      class="header-container-line"
      style="width: 100vw; margin-left: calc(-50vw + 50%); padding: 10px"
    ></div>

    <div class="student-info" style="margin-top: 50px">
      <div class="student-details-grid">
        <div class="student-col">
          <strong>รหัสนิสิต :</strong> {{ studentCode }}
        </div>
        <div class="student-col">
          <strong>ชื่อ:</strong> {{ studentData.thaiName }}
        </div>
        <div class="student-col"><strong>ปีการศึกษา:</strong> 2024</div>
        <div class="student-col">
          <strong>วันที่สำเร็จการศึกษา:</strong> 01/05/2025
        </div>
      </div>
      <div class="student-details-grid">
        <div class="student-col">
          <strong>Student ID:</strong> {{ studentCode }}
        </div>
        <div class="student-col">
          <strong>Name:</strong> {{ studentData.engName }}
        </div>
        <div class="student-col"><strong>Academic Year:</strong> 2024</div>
        <div class="student-col">
          <strong>Date of Graduation:</strong> 01/05/2025
        </div>
      </div>
    </div>

    <q-separator inset style="width: 90%; margin: 3% auto" />

    <div class="skills-container">
      <div class="chart-box">
        <h6 class="chart-title">Top 8 Specific Skills</h6>
        <SpecificSkillChart />
      </div>
      <div class="skill-list">
        <h6 class="chart-title">Specific Skills</h6>
        <q-separator inset style="margin: 3% auto" />
        <div class="skill-grid">
          <div
            v-for="(skill, index) in specificSkills"
            :key="index"
            class="skill-card specific q-pa-md relative-position"
          >
            <!-- ชื่อและ level -->
            <span class="skill-name specific">{{ skill.name }}</span>
            <p class="skill-achievement">Achieved: {{ skill.level }}</p>

            <q-btn
              dense
              round
              flat
              size="sm"
              icon="more_vert"
              v-if="hasSubskills(skill.id)"
              @click.stop="openSkillDialog(skill.id)"
              class="absolute-top-right q-mt-xs q-mr-xs"
            >
              <q-menu transition-show="jump-down" transition-hide="jump-up">
                <q-card
                  v-if="selectedSkill && selectedSkill.subskills.length > 0"
                  style="
                    min-width: 350px;
                    max-width: fit-content;
                    padding: 20px;
                  "
                  class="sub-skill-card"
                >
                  <q-card-section>
                    <div class="text-h6">{{ selectedSkill?.name }}</div>
                    <div class="flex row q-gutter-sm">
                      <div>Domain: {{ selectedSkill?.domain }}</div>
                      <div>Level: {{ selectedSkill?.gained }}</div>
                    </div>
                  </q-card-section>
                  <q-separator />
                  <q-card-section>
                    <SkillTreeTranscript :skill="selectedSkill" />
                  </q-card-section>
                </q-card>
              </q-menu>
            </q-btn>
          </div>
        </div>

        <div class="skill-levels">
          Level 1 Observe | Level 2 Practitioner | Level 3 Competent | Level 4
          Expert | Level 5 Master
        </div>
      </div>
    </div>

    <div class="skills-container" style="margin-top: 10%">
      <div class="chart-box">
        <h6 class="chart-title">Top 5 Soft Skills</h6>

        <SoftSkillChart />
      </div>
      <div class="skill-list">
        <h6 class="chart-title">Soft Skills</h6>
        <q-separator inset style="margin: 3% auto" />
        <div class="skill-grid">
          <div
            v-for="(skill, index) in softSkills"
            :key="index"
            class="skill-card specific q-pa-md relative-position"
          >
            <!-- ชื่อและ level -->
            <span class="skill-name specific">{{ skill.name }}</span>
            <p class="skill-achievement">Achieved: {{ skill.level }}</p>

            <q-btn
              dense
              round
              flat
              size="sm"
              icon="more_vert"
              v-if="hasSubskills(skill.id)"
              @click.stop="openSkillDialog(skill.id)"
              class="absolute-top-right q-mt-xs q-mr-xs"
            >
              <q-menu transition-show="jump-down" transition-hide="jump-up">
                <q-card
                  v-if="selectedSkill && selectedSkill.subskills.length > 0"
                  style="
                    min-width: 350px;
                    max-width: fit-content;
                    padding: 20px;
                  "
                  class="sub-skill-card"
                >
                  <q-card-section>
                    <div class="text-h6">{{ selectedSkill?.name }}</div>
                    <div class="flex row q-gutter-sm">
                      <div>Domain: {{ selectedSkill?.domain }}</div>
                      <div>Level: {{ selectedSkill?.gained }}</div>
                    </div>
                  </q-card-section>
                  <q-separator />
                  <q-card-section>
                    <SkillTreeTranscript :skill="selectedSkill" />
                  </q-card-section>
                </q-card>
              </q-menu>
            </q-btn>

            <!-- ไอคอน subskill ที่มุมขวาบน ถ้ามี -->
            <!-- <q-icon
              v-if="hasSubskills(skill.id)"
              name="expand_more"
              size="20px"
              class="absolute-top-right q-mt-sm q-mr-sm cursor-pointer"
              @click.stop="openSkillDialog(skill.id)"
              color="primary"
            /> -->
          </div>
        </div>
        <!-- <p class="english-level">English (CEFR-level) B1</p> -->

        <div class="skill-levels">
          Level 1 Emerging | Level 2 Established | Level 3 Exemplary
        </div>
      </div>
    </div>
    <q-separator inset style="width: 90%; margin-top: 7%" />

    <div
      class="skills-container final-verification"
      style="max-height: 250px; margin-bottom: 7%"
      v-if="isExporting"
    >
      <div class="chart-box verification-box">
        <div class="verification-label">
          Key Skills: Data Pipeline, Data Visualization
        </div>
        <q-img
          src="approved_stamp.png"
          height="300px"
          width="300px"
          fit="contain"
        />
      </div>

      <div class="skill-list verification-box">
        <q-separator inset style="width: 90%; margin-top: 190px" />
        <div class="skill-levels">University Registrar</div>
        <div class="skill-levels note">
          Verify this document’s authenticity at: www.buu.ac.th/verify
        </div>
      </div>

      <div class="skill-list verification-box">
        <div class="skill-levels note" style="margin-top: 280px">
          Document No: ST2024-XXXX
        </div>
      </div>
    </div>
    <div class="row justify-end q-mt-md">
      <q-btn
        v-if="!isExporting"
        label="Download PDF"
        color="primary"
        @click="downloadPDF"
      />
    </div>

    <div
      class="header-container-line"
      style="
        padding: 40px;
        margin-top: 20px;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-bottom: -16px;
      "
    ></div>
  </q-page>
  <!-- <q-dialog
    v-if="selectedSkill && selectedSkill.subskills.length > 0"
    v-model="dialog"
  >
    <q-card style="min-width: 350px; max-width: fit-content; padding: 20px">
      <q-card-section>
        <div class="text-h6">{{ selectedSkill?.name }}</div>
        <div class="flex row q-gutter-sm">
          <div>Domain: {{ selectedSkill?.domain }}</div>
          <div>Level: {{ selectedSkill?.gained }}</div>
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section>
        <SkillTreeTranscript :skill="selectedSkill" />
      </q-card-section>
    </q-card>
  </q-dialog> -->
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import SpecificSkillChart from 'src/features/transcript/SpecificSkillChart.vue';
import SoftSkillChart from 'src/features/transcript/SoftSkillChart.vue';
import { SkillCollectionService } from 'src/services/skill-collections';
import { StudentService } from 'src/services/student';
import { useRoute } from 'vue-router';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useRouter } from 'vue-router';
import { BranchService } from 'src/services/branch';
import SkillTreeTranscript from 'src/components/SkillTreeTranscript.vue';
import type { Branch, Student } from 'src/types/education';

const router = useRouter();

export interface SkillItem {
  id: number;
  name: string;
  domain: string;
  gained: number;
  subskills: SkillItem[];
}
interface DisplaySkill {
  id: number;
  name: string;
  level: string;
  score?: number;
}

const branch = ref<Branch | null>(null);
const topSpecificSkills = ref<DisplaySkill[]>([]);
const topSoftSkills = ref<DisplaySkill[]>([]);
const isExporting = ref(false);
const data = ref<{ specific: SkillItem[]; soft: SkillItem[] }>();
const studentData = ref<Student>({} as Student);
const allSkills = ref<SkillItem[]>([]);
const specificSkills = ref<DisplaySkill[]>([]);
const softSkills = ref<DisplaySkill[]>([]);
const route = useRoute();
const studentCode = computed(() => String(route.params.code));
const selectedSkill = ref<SkillItem | null>();

watch(
  () => route.params.code,
  (newCode, oldCode) => {
    if (newCode !== oldCode) {
      window.location.reload();
    }
  },
);

const loadTranscript = async () => {
  try {
    data.value = await SkillCollectionService.fetchTranscript(
      studentCode.value,
    );
    studentData.value = await StudentService.getOne(studentCode.value);
    if (studentData.value.branchId) {
      branch.value = await BranchService.getOne(studentData.value.branchId);
    }

    allSkills.value.push(...(data.value?.specific || []));
    allSkills.value.push(...(data.value?.soft || []));

    specificSkills.value =
      data.value?.specific.map((item) => ({
        id: item.id,
        name: item.name,
        level: mapScoreToLevel(item.gained),
        score: item.gained,
      })) || [];

    // remove score 0
    specificSkills.value = specificSkills.value.filter(
      (skill) => skill.score !== 0,
    );
    console.log('specificSkills.value', specificSkills.value);

    softSkills.value =
      data.value?.soft.map((item) => ({
        id: item.id,
        name: item.name,
        level: mapSoftScoreToLevel(item.gained),
        score: item.gained,
      })) || [];

    // remove score 0
    softSkills.value = softSkills.value.filter((skill) => skill.score !== 0);

    if (!data.value || !studentData.value || !studentData.value.code) {
      throw new Error('Invalid student data');
    }
  } catch (error) {
    console.error('ไม่พบข้อมูลนิสิต หรือเกิดข้อผิดพลาด:', error);
    void router.replace('/error-not-found');
  }
};
onMounted(async () => {
  await loadTranscript();
});

function mapScoreToLevel(score: number): string {
  if (score >= 5) return 'Master';
  if (score === 4) return 'Expert';
  if (score === 3) return 'Competent';
  if (score === 2) return 'Practitioner';
  return 'Observe';
}

function mapSoftScoreToLevel(score: number): string {
  if (score >= 3) return 'Exemplary';
  if (score === 2) return 'Established';
  return 'Emerging';
}

function hasSubskills(skillId: number): boolean {
  const fullSkill = allSkills.value?.find((s) => s.id === skillId);
  return fullSkill?.subskills && fullSkill.subskills.length > 0 ? true : false;
}

function openSkillDialog(skillId: number) {
  const fullSkill = allSkills.value?.find((s) => s.id === skillId);
  if (fullSkill && fullSkill.subskills.length > 0) {
    selectedSkill.value = fullSkill;
  }
}

const downloadPDF = async () => {
  isExporting.value = true;

  topSpecificSkills.value = [...specificSkills.value]
    .sort((a, b) => (b.score ?? 0) - (a.score ?? 0))
    .slice(0, 8);

  topSoftSkills.value = [...softSkills.value]
    .sort((a, b) => (b.score ?? 0) - (a.score ?? 0))
    .slice(0, 5);

  const originalSpecific = [...specificSkills.value];
  const originalSoft = [...softSkills.value];

  specificSkills.value = [...topSpecificSkills.value];
  softSkills.value = [...topSoftSkills.value];

  await nextTick();
  await new Promise((r) => setTimeout(r, 300));

  const target = document.querySelector('.q-page') as HTMLElement;
  if (!target) return;

  const canvas = await html2canvas(target, {
    scale: 3,
    useCORS: true,
  });

  const imgData = canvas.toDataURL('image/png');
  const pdf = new jsPDF('p', 'mm', 'a4');

  const imgProps = pdf.getImageProperties(imgData);
  const pdfWidth = pdf.internal.pageSize.getWidth();
  const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

  const marginY = 0;
  const scaleUp = 1.2;

  pdf.addImage(
    imgData,
    'PNG',
    -((pdfWidth * (scaleUp - 1)) / 2),
    marginY,
    pdfWidth * scaleUp,
    pdfHeight * scaleUp,
  );

  pdf.save(`transcript-${studentCode.value}.pdf`);

  specificSkills.value = [...originalSpecific];
  softSkills.value = [...originalSoft];
  isExporting.value = false;
};
</script>

<style scoped>
.q-page {
  color: #1a237e;
  background: #fff;
}
.full-width-line {
  width: 100vw;
  height: 8px;
  background: linear-gradient(120deg, #1e3a8a, #2563eb);
  margin-left: calc(-50vw + 50%);
  margin-top: -16px;
}

.background-pattern {
  background: linear-gradient(135deg, #ffffff, #f3f3f3);
  background-size: cover;
}

.header-bg {
  width: 100vw;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
}

.header-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  gap: 24px;
  width: 90%;
  max-width: 1400px;
  background-color: #ffffff;
}

.student-info,
.skills-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  gap: 24px;
  margin: 30px auto;
  width: 80%;
  margin-top: 5%;
}
.header-container-line {
  background: linear-gradient(120deg, #1e3a8a, #2563eb);
  height: 20px;
  width: 100%;
}
.logo-container,
.student-photo-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
}
.buu-logo {
  border-radius: 50%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  background: radial-gradient(circle, #ffffff, #e2e8f0);
  padding: 8px;
  transition: transform 0.3s ease;
}

.title-container {
  flex: 2;
  text-align: center;
  margin-top: -20px;
}
.title {
  font-size: 45px;
  font-weight: bold;
  margin-bottom: 12px;
}
.subtitle {
  font-size: 14px;
  margin-top: 6px;
  line-height: 1.4;
}
.faculty {
  font-size: 18px;
  font-weight: bold;
  margin-top: 4px;
}
.graduate-photo {
  border-radius: 12px;
  border: 4px solid white;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
.student-details {
  font-size: 16px;
  color: #1e293b;
  line-height: 1.6;
  text-align: center;
  margin-top: 10px;
}
.chart-box {
  flex: 1;
  min-width: 280px;
  max-width: 400px;
  text-align: center;
  height: 420px;
}
.skill-list {
  flex: 2;
  min-width: 300px;
  margin-left: 10%;
  height: 420px;
}
.skill-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  padding-top: 10px;
  max-height: 360px;
  overflow-y: auto;
  padding-right: 8px;
}

.skill-card {
  padding: 16px 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 160px;
  flex: 1 1 calc(33% - 16px);
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}
.skill-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
}
.skill-card.soft {
  background: linear-gradient(145deg, #dcffe1, #ffffff);
  border: 1px solid #a5e6c4;
  box-shadow: 0 4px 14px rgba(0, 128, 96, 0.1);
}
.skill-card.specific {
  background: linear-gradient(145deg, #e5ecf5, #ffffff);
  border: 1px solid #acc8ec;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
}
.skill-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 6px;
  display: block;
}
.skill-name.specific {
  color: #1a237e;
}
.skill-name.soft {
  color: #0f7a01;
}
.skill-achievement {
  font-size: 14px;
  color: #475569;
}
.chart-title {
  color: #000000;
  font-weight: bold;
  margin-bottom: 12px;
  margin-top: 10px;
}
.skill-levels,
.english-level {
  text-align: center;
  font-weight: bold;
  margin-top: 50px;
  color: #1e293b;
}
@media (max-width: 768px) {
  .skill-card {
    flex: 1 1 100%;
  }
  .title {
    font-size: 28px;
  }
}

.sub-skill-card {
  padding: 16px 20px;
  border-radius: 12px;
  min-width: 160px;
  flex: 1 1 calc(33% - 16px);
  background: linear-gradient(145deg, #e5ecf5, #ffffff);
  border: 1px solid #acc8ec;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
}

.student-details-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px 80px;
  font-size: 18px;
  color: #1e293b;
  margin: auto;
  text-align: left;
}
.student-col strong {
  color: #1e40af;
}

.final-verification {
  margin-top: 60px;
  gap: 32px;
}

.skill-levels.note {
  font-size: 14px;
  margin-top: 10px;
  color: #64748b;
  text-align: center;
}
.relative-position {
  position: relative;
}
</style>
