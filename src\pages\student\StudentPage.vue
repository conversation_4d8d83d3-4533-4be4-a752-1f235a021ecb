<template>
  <q-page :padding="computedPadding">
    <PageHeader
      v-model:search-text="filterStore.filters['students'].nameCode"
      hide-add-btn
      :hide-faculty="hideFacultyFilter"
      import-btn
      @open-dialog-import="handleOpenDialogImport"
      :label-search="t('search') + t('students')"
      @enter-search="store.fetchAll(store.pagination)"
    />
    <!-- Table -->
    <q-table
      flat
      bordered
      class="q-animate--fade page-table q-mt-lg"
      separator="cell"
      :rows="store.getStudents"
      row-key="code"
      v-model:pagination="store.pagination"
      :loading="global.getLoadingState"
      :columns="studentColumns"
      @request="(p) => store.fetchAll(p.pagination)"
    >
      <template #body-cell-actions="props">
        <ActionsCell
          v-on:handle-edit="handleEdit(props.row)"
          v-on:handle-delete="store.handleRemove(props.row)"
        >
          <template #prefix>
            <q-btn
              label="transcript"
              unelevated
              color="primary"
              class="text-caption"
              @click="onClickTranscript(props.row)"
            />
            <q-btn
              icon="link"
              unelevated
              class="q-pa-none"
              @click="onClickEvaluation(props.row)"
            />
          </template>
        </ActionsCell>
      </template>
    </q-table>
  </q-page>
</template>

<script lang="ts" setup>
import { useMeta, useQuasar } from 'quasar';
import { computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStudentStore } from 'src/stores/student';
import { useGlobalStore } from 'src/stores/global';
import { useI18n } from 'vue-i18n';
import ActionsCell from 'src/components/table/ActionsCell.vue';
import StudentForm from 'src/features/student/dialog/StudentForm.vue';
import ImportStudent from 'src/features/subject/dialog/ImportStudent.vue';
import PageHeader from 'src/components/common/PageHeader.vue';
import type { Student } from 'src/types/education';
import useFilterStore from 'src/stores/filter-search';
import { studentColumns } from 'src/data/column_table';

const filterStore = useFilterStore();
const $q = useQuasar();
const { t } = useI18n();
const global = useGlobalStore();
const store = useStudentStore();
const route = useRoute();
const router = useRouter();
const title = computed(() => route.matched[1]?.name as string);

const handleEdit = (item: Student) => {
  store.dialog.title = 'form.Edit Student';
  store.form = { ...item };
  $q.dialog({ component: StudentForm });
};

const onClickTranscript = async (student: Student) => {
  await router.push('/transcript/' + student.code);
};

const onClickEvaluation = async (student: Student) => {
  await router.push('/evaluation/' + student.code);
};

const handleOpenDialogImport = () => {
  $q.dialog({
    component: ImportStudent,
  });
};

const props = defineProps<{
  noPadding?: true;
  curriculumId?: number;
}>();

const computedPadding = computed(() => (!props.noPadding));
const hideFacultyFilter = computed(
  () => title.value === 'Students of Curriculum',
);

onMounted(async () => {
  if (props.curriculumId) {
    filterStore.setPage('student_curriculum');
    filterStore.updateFilter({ curriculumId: Number(props.curriculumId) });
  } else {
    filterStore.setPage('students');
  }
  await store.fetchAll(store.pagination);
});

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>
