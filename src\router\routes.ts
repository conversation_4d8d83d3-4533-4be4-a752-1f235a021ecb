import type { RouteRecordRaw } from 'vue-router';
import { publicRoutes } from './publicRoutes';
import { curriculumRoutes } from './curriculumRoutes';
import { routes as combinedRoutes } from './combinedRoutes';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'root',
    component: () => import('src/layouts/RootLayout.vue'),
    meta: { public: true },
  },
  ...curriculumRoutes,
  ...publicRoutes,
  ...combinedRoutes,
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('src/pages/public/ErrorNotFound.vue'),
  },
];

export default routes;
