<template>
    <div class="q-pa-md">
        <div class="q-mb-md">
            <q-btn flat icon="arrow_back" label="Back to Summary" @click="goBack" class="q-mb-md" />
        </div>

        <div v-if="skill" class="q-mb-md">
            <h5 class="q-my-none">{{ skill.thaiName }}</h5>
            <p class="text-grey-7 q-mb-none">
                {{ skill.engName }}
                <!-- <span v-if="category" class="q-ml-md">
                    Category: {{ category }}
                </span>
                <span v-if="year" class="q-ml-md">
                    Year: {{ year }}
                </span> -->
            </p>
        </div>

        <PageHeader v-model:search-text="filterStore.filters['students'].nameCode" hide-add-btn hide-filter
            :hide-faculty="hideFacultyFilter" :label-search="t('search') + t('students')" />
        <q-table flat bordered class="q-animate--fade page-table q-mt-lg" separator="cell" :rows="rowsData"
            row-key="code" :loading="global.getLoadingState" :columns="studentSummaryColumns">

        </q-table>
    </div>
</template>

<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import { CurriculumService } from 'src/services/curriculums';
import type { Skill, SummaryStudent } from 'src/types/education';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { useGlobalStore } from 'src/stores/global';
import { useSkillStore } from 'src/stores/skill';
import { useCurriculumStore } from 'src/stores/curriculum';
import PageHeader from 'src/components/common/PageHeader.vue';
import useFilterStore from 'src/stores/filter-search';
import SkillService from 'src/services/skill';

const global = useGlobalStore();
const filterStore = useFilterStore();

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
// Get parameters from route
const skillId = computed(() => Number(route.params.skillId));
const category = computed(() => route.query.category as string);
const year = computed(() => route.query.year as string);

const curriculumId = useCurriculumStore().getInsertId ?? -1;
const rowsData = ref<SummaryStudent[]>([]);
const skill = ref<Skill>();
const skillStore = useSkillStore()
const title = computed(() => route.matched[1]?.name as string);

const hideFacultyFilter = computed(
    () => title.value === 'Students of Curriculum',
);
// Navigation function to go back
const goBack = () => {
    void router.push({
        name: 'Summary of Curriculum',
        params: { code: route.params.code },
        query: {
            skillType: route.query.skillType,
            year: route.query.year
        }
    });
};
// function getLeafSkills(skill: Skill): Skill[] {
//     if (!skill.subs || skill.subs.length === 0) {
//         return [skill]; // leaf node
//     }

//     return skill.subs.flatMap(getLeafSkills); // recursive
// }

const studentSummaryColumns = ref<QTableColumn[]>([
    {
        name: 'code',
        label: t('code'),
        field: 'code',
        align: 'left',
    },
    {
        name: 'name',
        label: t('name'),
        field: 'thaiName',
        align: 'left',
    },
    {
        name: 'leafSkills',
        label: t('skills'),
        field: (row: SummaryStudent) => row.skill_collections.map(s => s.clo.skill?.engName).join(', '),
        align: 'left'
    },
    {
        name: 'level',
        label: t('gainedLevel'),
        field: (row: SummaryStudent) => row.skill_collections.map(s => s.gainedLevel).join(', '),
        align: 'left',
    },
    {
        name: 'expectedlevel',
        label: t('expectedLevel'),
        field: (row: SummaryStudent) => row.skill_collections.map(s => s.clo.expectSkillLevel),
        align: 'left',
    },
]);

// Function to fetch student data
const fetchStudentData = async () => {
    console.log('Fetching student data with params:', {
        skillId: skillId.value,
        year: year.value,
        category: category.value
    });

    // Only fetch if we have all required parameters
    if (skillId.value && year.value) {
        try {
            const res = await CurriculumService.getStudentSummary(skillId.value, year.value, category.value);
            rowsData.value = res.data;
            console.log(rowsData.value)
        } catch (error) {
            console.error('Error fetching student summary:', error);
            rowsData.value = [];
        }
    } else {
        console.warn('Missing required parameters for fetching student data:', {
            skillId: skillId.value,
            year: year.value
        });
    }
};

const fetchSkill = async () => {
    if (skillId.value) {
        try {
            const res = await SkillService.getOne(skillId.value);
            skill.value = res;
        } catch (error) {
            console.error('Error fetching skill name:', error);
            skill.value = undefined;
        }
    }
};

// Watch for year changes and refetch data
watch(() => year.value, (newYear) => {
    if (newYear) {
        void fetchSkill();
        void fetchStudentData();
    }
}, { immediate: false });

// Watch for category changes and refetch data
watch(() => category.value, () => {
    void fetchSkill();
    void fetchStudentData();
}, { immediate: false });

onMounted(async () => {
    // Fetch skills first
    await skillStore.fetchAllInCurr(undefined, curriculumId);

    // Then fetch student data
    await fetchStudentData();
    await fetchSkill();
});



</script>