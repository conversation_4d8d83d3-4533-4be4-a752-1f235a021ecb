<template>
  <DialogForm
    :title="store.form.id ? t('form.Edit Skill') : t('form.New Skill')"
    @save="store.handleSave"
    v-model="store.dialogForm"
    :json="store.form"
    ref="formRef"
  >
    <div class="q-gutter-y-md">
      <q-input
        v-model="store.form.thaiName"
        :label="t('name')"
        outlined
        :rules="[requireField]"
      />
      <q-input
        v-model="store.form.engName"
        :label="t('engName')"
        outlined
        :rules="[requireField]"
      />
      <q-select
        :options="Object.values(LearningDomain)"
        v-model="store.form.domain"
        label="Domain"
        outlined
        @update:model-value="
          () => {
            formRef.validateForm();
          }
        "
        :rules="[requireField]"
      />
      <q-input
        v-model="store.form.thaiDescription"
        :label="t('description')"
        outlined
        type="textarea"
        counter
        maxlength="1000"
        :rules="[requireField]"
      />
      <q-input
        v-model="store.form.engDescription"
        :label="t('engDescription')"
        outlined
        counter
        maxlength="1000"
        type="textarea"
        :rules="[requireField]"
      />
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import { requireField } from 'src/utils/field-rules';
import DialogForm from 'src/components/form/DialogForm.vue';
import { useSkillStore } from 'src/stores/skill';
import { useI18n } from 'vue-i18n';
import { ref } from 'vue';
import { LearningDomain } from 'src/types/education';

const formRef = ref();
const store = useSkillStore();
const { t } = useI18n();
</script>

<style scoped></style>
