<script setup lang="ts">
/*
    imports
*/
import { computed, onUnmounted, ref } from 'vue';
import { QInfiniteScroll, QTree, useMeta } from 'quasar';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useSkillStore } from 'src/stores/skill';
import useFilterStore from 'src/stores/filter-search';
import PageHeader from 'src/components/common/PageHeader.vue';
import SkillDialog from '../dialog/SkillForm.vue';
import PageTitle from 'src/components/common/PageTitle.vue';
import ChipSkill from 'src/components/common/ChipSkill.vue';
/*
    states
*/
const store = useSkillStore();
const filterStore = useFilterStore();
const { t } = useI18n();
const route = useRoute();
const title = computed(() => route.matched[1]?.name as string);
const loading = ref(false);
const infiniteScroll = ref();
/*
    methods
*/

onUnmounted(() => {
  store.skills = [];
});

useMeta({
  title: title.value,
});
const fetchByPage = async (index: number, done: (stop?: boolean) => void) => {
  try {
    const pagination = { ...store.pagination, page: index };

    await store.fetchAllInCurr(pagination);

    // Check if there is no data or if the current page exceeds the max page
    if (store.getSkills.length === 0 || store.getMaxPage <= index) {
      infiniteScroll.value?.stop();
      done(true); // Stop infinite scroll
    } else {
      done(); // Continue loading
    }
  } catch (error) {
    console.error('Error fetching skills:', error);
    done(true); // Stop on error
  }
};
</script>

<template>
  <div class="q-gutter-y-md q-mt-md">
    <PageTitle :title="t('menuLinks.skills')" />
    <PageHeader
      v-model:search-text="filterStore.search"
      @open-dialog="store.toggleDialog({ title: 'form.New Skill', index: -1 })"
      hide-filter
      hide-search
    >
      <template #top-left>
        <div class="text-grey-7">{{ t('totalSkills') }}: {{ store.total }}</div>
      </template>
    </PageHeader>
    <SkillDialog />
    <!-- Content -->
    <div class="q-mt-md" v-if="!loading">
      <div class="row q-mr-lg">
        <q-scroll-area
          class="col q-mr-md q-pr-lg"
          style="height: calc(100vh - 300px)"
        >
          <q-infinite-scroll
            @load="fetchByPage"
            ref="infiniteScroll"
            :offset="100"
          >
            <q-tree
              :nodes="store.getSkills"
              node-key="id"
              children-key="subs"
              class="q-animate--fade"
              :no-nodes-label="t('noData')"
            >
              <template v-slot:default-header="props">
                <q-tr
                  class="justify-between full-width flex hover-row"
                  style="cursor: pointer"
                  @click="store.setSelectedItem(props.node)"
                >
                  <q-td style="user-select: none">
                    <div class="text-body1">
                      {{ props.node.thaiName ?? 'No Thai Name' }}
                      <ChipSkill :domain="props.node.domain" />
                    </div>
                    <div class="text-caption">
                      {{ props.node.engName ?? 'No Eng Name' }}
                    </div>
                  </q-td>
                  <q-td class="q-gutter-x-sm flex">
                    <q-btn
                      @click="
                        store.toggleDialog({
                          parent: props.node,
                          title: 'Insert Sub-Skill',
                          index: props.index,
                        })
                      "
                      icon="img:svg/add-child.svg"
                      padding="none"
                      class="hover-btn"
                      flat
                    >
                      <q-tooltip>{{ t('skillTab.insert-sub-skill') }}</q-tooltip>
                    </q-btn>
                    <q-btn
                      @click="
                        store.toggleDialog({
                          form: props.node,
                          title: 'form.Edit Skill',
                          index: props.index,
                        })
                      "
                      icon="edit"
                      padding="none"
                      class="hover-btn"
                      flat
                    >
                      <q-tooltip>{{ t('skillTab.edit') }}</q-tooltip></q-btn
                    >
                    <q-btn
                      @click="store.handleRemove(props.node)"
                      icon="delete"
                      padding="none"
                      class="hover-btn"
                      flat
                    >
                      <q-tooltip>{{ t('skillTab.delete') }}</q-tooltip>
                    </q-btn>
                  </q-td>
                </q-tr>
              </template>
            </q-tree>
            <template #loading>
              <div
                v-if="store.getSkills.length > 0"
                class="row justify-center q-my-md"
              >
                <q-spinner-dots color="primary" size="40px" />
              </div>
            </template>
          </q-infinite-scroll>
        </q-scroll-area>
        <q-scroll-area class="col" style="height: calc(100vh - 300px)">
          <q-card bordered class="q-pa-sm" flat>
            <!-- ส่วนหัว -->
            <q-card-section v-if="store.getSelectedItem">
              <div class="text-h5">{{ store.getSelectedItem.thaiName }}</div>
              <!-- ขยายขนาด -->
              <div class="text-body1 text-grey-8 q-mt-sm">
                {{ store.getSelectedItem.engName }}
              </div>
              <q-separator class="q-my-sm" />
              <!-- เส้นคั่น -->
              <div
                v-if="store.getSelectedItem && store.getSelectedItem.domain"
                class="q-pt-none"
              >
                <q-chip
                  square
                  color="primary"
                  text-color="white"
                  style="
                    border-radius: 12px;
                    padding: 6px 12px;
                    text-align: center;
                  "
                >
                  {{ store.getSelectedItem.domain }}
                </q-chip>
              </div>
              <div
                v-if="store.getSelectedItem.thaiDescription"
                class="q-mt-sm q-gutter-y-sm rounded-borders q-pa-sm"
              >
                <div
                  class="text-body1"
                  style="
                    white-space: pre-line;
                    text-indent: 1.2em;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                  "
                >
                  {{ store.getSelectedItem.thaiDescription }}
                </div>
                <div
                  class="text-body1"
                  style="
                    white-space: pre-line;
                    text-indent: 1.2em;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                  "
                >
                  {{ store.getSelectedItem.engDescription }}
                </div>
              </div>
            </q-card-section>
            <q-card-section v-else>
              <div class="text-grey-6 text-center">กรุณาเลือกข้อมูล</div>
            </q-card-section>
          </q-card>
        </q-scroll-area>
      </div>
      <div class="text-caption flex items-center q-mt-md">
        <q-icon name="warning" size="xs" color="grey-7"></q-icon>
        <span class="q-ml-xs">Max Deep Level: 5</span>
      </div>
    </div>
    <div
      v-else
      class="row q-mt-md q-gutter-x-lg"
      style="height: calc(100vh - 300px)"
    >
      <q-skeleton class="col-6" type="rect" />
      <q-skeleton class="col" type="rect" />
    </div>
  </div>
</template>

<style scope lang="scss">
.hover-row:hover {
  color: $secondary;
}
.hover-btn {
  color: $grey-6;
}
.hover-btn:hover {
  color: $grey-5;
}
</style>
