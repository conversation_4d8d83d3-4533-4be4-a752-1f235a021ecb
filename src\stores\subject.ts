import { defineStore } from 'pinia';
import { Dialog, Notify, type QTableProps } from 'quasar';
import { useRouter } from 'vue-router';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import { SubjectService } from 'src/services/subject';
import { useCurriculumStore } from 'src/stores/curriculum';
import { nextTick } from 'vue';
import { updateTable } from 'src/utils/table';
import { i18n } from 'src/boot/i18n';
import type { QueryParams } from 'src/types/api';
import type { Subject } from 'src/types/education';
import useFilterStore from './filter-search';

const { t } = i18n.global;

type TitleForm = 'form.New Subject' | 'form.Edit Subject';
export const useSubjectStore = defineStore('subject', {
  state: () => ({
    dialogState: false,
    form: {} as Subject,
    subjects: [] as Subject[],
    tabsModel: 'req',
    editMode: true,
    titleForm: '' as TitleForm,
    search: '',
    rowIndex: -1,
    pagination: { ...defaultPagination },
    filterModel: {} as Partial<QueryParams>,
    router: useRouter(),
    curr: useCurriculumStore(),
    subjectCodeLabel: '',
    foundExistSubject: false,
    total: 0,
  }),
  getters: {
    getDialogTitle: (s) => s.titleForm,
    getListSubjects: (s) => s.subjects,
    getSubjectCodeLabel: (s) => s.subjectCodeLabel,
  },
  actions: {
    async checkSubjectCode(subjectCode: string) {
      const { t } = i18n.global;
      if (subjectCode.length === 8) {
        const existSubject =
          await SubjectService.findExistSubjectCode(subjectCode);
        if (existSubject) {
          this.subjectCodeLabel = t('validation.existCode');
          this.foundExistSubject = true;
          this.form = existSubject;
        } else {
          this.subjectCodeLabel = t('validation.availableCode');
          this.foundExistSubject = false;
        }
      } else {
        this.subjectCodeLabel = '';
      }
    },
    async fetchAll(pag?: QTableProps['pagination']) {
      try {
        const filterStore = useFilterStore();
        this.filterModel = filterStore.filters['subjects'];
        this.pagination = pag || this.pagination;
        const { data, total } = await SubjectService.getAll(
          convertToQuery(this.pagination, this.filterModel),
        );
        this.subjects = JSON.parse(JSON.stringify(data));
        this.pagination.rowsNumber = total;
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    },
    async fetchAllInCurr(
      pag?: QTableProps['pagination'],
      curriculumId?: number | string,
    ) {
      try {
        const currId = curriculumId || this.curr.getInsertId;
        this.filterModel.curriculumId = currId;
        this.pagination = pag || this.pagination;
        const filter = convertToQuery(this.pagination, this.filterModel);

        const { data, total } = await SubjectService.getAll(filter);

        this.subjects = JSON.parse(JSON.stringify(data));
        this.total = total;
        this.pagination.rowsNumber = total;
      } catch (error: unknown) {
        console.error('Error fetching data:', error);
      }
    },
    resetForm() {
      this.form = {} as Subject;
    },

    async createOne() {
      try {
        const data = await SubjectService.createOne(this.form);
        if (data) {
          Notify.create({
            type: 'ok',
            message: t('notify.createSuccess'),
          });
          this.subjects = updateTable(data, this.subjects, 'create');
        }
      } catch (error) {
        Notify.create({
          type: 'negative',
          message: t('notify.createError'),
        });
        console.error('Error creating subject:', error);
      }
    },

    async updateOne() {
      try {
        const data = await SubjectService.updateOne(this.form.id, this.form);
        if (data) {
          Notify.create({
            type: 'ok',
            message: t('notify.updateSuccess'),
          });
          this.subjects = updateTable(data, this.subjects, 'update');
        }
      } catch (error) {
        Notify.create({
          type: 'negative',
          message: t('notify.updateError'),
        });
        console.error('Error updating subject:', error);
      }
    },

    async deleteOne(id: number) {
      try {
        await SubjectService.removeOne(id);
        Notify.create({
          type: 'ok',
          message: t('notify.deleteSuccess'),
        });
        this.subjects = updateTable({ id } as Subject, this.subjects, 'delete');
      } catch (error) {
        Notify.create({
          type: 'negative',
          message: t('notify.deleteError'),
        });
        console.error('Error deleting subject:', error);
      }
    },

    handleDelete(id: number) {
      Dialog.create({
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this Subject?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        void nextTick(async () => {
          await this.deleteOne(id);
        });
      });
    },

    async handleSave() {
      if (!this.form.curriculumId && this.curr.getInsertId) {
        this.form.curriculumId = this.curr.getInsertId;
      }
      if (this.titleForm === 'form.Edit Subject') {
        await this.updateOne();
      } else {
        await this.createOne();
      }
      this.dialogState = false;
      this.resetForm();
    },

    handleEdit(form: Partial<Subject>) {
      this.form = JSON.parse(JSON.stringify(form));
      this.titleForm = 'form.Edit Subject';
    },

    handleCreate() {
      this.form = {} as Subject;
      this.titleForm = 'form.New Subject';
    },
  },
});
