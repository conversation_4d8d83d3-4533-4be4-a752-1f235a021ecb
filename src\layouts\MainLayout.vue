<template>
  <q-layout view="hHh lpR fFf">
    <AppHeader />
    <!-- Left Drawer -->
    <AppDrawer>
      <q-list>
        <MenuLink v-for="link in userMenu" :key="link.title" v-bind="link" />
      </q-list>
    </AppDrawer>

    <!-- Right Drawer -->
    <AppRightDrawer />

    <!-- Main Content View -->
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import MenuLink from 'src/components/ui/MenuLink.vue';
import { useQuasar , LocalStorage } from 'quasar';
import AppHeader from 'src/components/ui/AppHeader.vue';
import AppDrawer from 'src/components/ui/AppLeftDrawer.vue';
import AppRightDrawer from 'src/components/ui/AppRightDrawer.vue';
import { getMenuByRole } from 'src/data/menu';
import { useAuthStore } from 'src/stores/auth';

const darkRef = ref(false);
const auth = useAuthStore();

// Get menu items based on user role
const userMenu = computed(() => {
  const userRole = auth.getRole;
  return userRole ? getMenuByRole(userRole) : [];
});

watch(
  () => darkRef.value,
  (val) => {
    dark.set(val);
    LocalStorage.set('theme', val ? 'dark' : 'light');
  },
);

const { dark } = useQuasar();

defineOptions({
  name: 'AdminLayout',
});
</script>
