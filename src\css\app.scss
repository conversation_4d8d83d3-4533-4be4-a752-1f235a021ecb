// app global css in SCSS form

a {
  text-decoration: none;
  color: black;
}

.page-table {
  height: calc(100vh - 200px);
}

.q-layout {
  background-color: $surface-primary;
}

.q-tooltip {
  font-size: 14px;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
}

@media screen and (max-width: 1024px) {
  .container {
    max-width: 100%;
    padding: 0 20px;
  }
  
}

.cell-head-number {
  width: 80px;
}

.hover-row:hover {
  color: $secondary;
}
.hover-btn {
  color: $grey-6;
}
.hover-btn:hover {
  color: $grey-5;
}

.highlight-1 {
  color: $primary;
  font-weight: bold;
}

.q-separator {
  width: 100%;
  height: 1px;
  border-radius: 100%;
}

.body--light {
  // .q-drawer {
  //   width: 250px !important;
  // }

  .q-header {
    background-color: $primary;
  }

  // .q-page-container {
  //   background-color: $surface-light;
  // }

  .q-table {
    // height: calc(100vh - 280px);

    .q-table__top,
    thead tr:first-child th {
      // background-color: $surface-secondary;
      font-weight: bold;
    }

    thead tr th {
      position: sticky;
      z-index: 1;
      font-size: 15px;
      background: $secondary;
      color: white;
    }

    td {
      font-size: 14px;
    }

    thead tr:first-child th {
      top: 0;
    }

    tr:nth-child(odd) {
      background-color: #fcfcfc;
    }

    tr:nth-child(even) {
      background-color: #f6f6f6;
    }

    tr:hover {
      background-color: #e3eafb;
    }
  }

  .q-table__bottom,
  .q-table__bottom .q-table__control .q-field__native,
  .q-table__bottom .q-table__control .q-field__append {
    // background-color: $surface-secondary;
    font-size: 13px;
  }
}

.body--dark {
  .q-toolbar {
    background-color: $primary-dark;
  }

  .q-table {
    .q-table__top,
    thead tr:first-child th {
      background-color: $primary-dark;
      color: white;
    }

    thead tr th {
      position: sticky;
      z-index: 1;
    }

    thead tr:first-child th {
      top: 0;
    }
  }

  .q-table__bottom {
    background-color: $primary-dark;
    color: white;
  }

  .q-field__control {
    color: $accent;
  }
}

.vjs-tree-brackets {
  cursor: pointer;
}
.vjs-tree-brackets:hover {
  color: #1890ff;
}
.vjs-check-controller {
  position: absolute;
  left: 0;
}
.vjs-check-controller.is-checked .vjs-check-controller-inner {
  background-color: #1890ff;
  border-color: #0076e4;
}
.vjs-check-controller.is-checked .vjs-check-controller-inner.is-checkbox:after {
  transform: rotate(45deg) scaleY(1);
}
.vjs-check-controller.is-checked .vjs-check-controller-inner.is-radio:after {
  transform: translate(-50%, -50%) scale(1);
}
.vjs-check-controller .vjs-check-controller-inner {
  display: inline-block;
  position: relative;
  border: 1px solid #bfcbd9;
  border-radius: 2px;
  vertical-align: middle;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  background-color: #fff;
  z-index: 1;
  cursor: pointer;
  transition:
    border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
    background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}
.vjs-check-controller .vjs-check-controller-inner:after {
  box-sizing: content-box;
  content: '';
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 8px;
  left: 4px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(0);
  width: 4px;
  transition: transform 0.15s cubic-bezier(0.71, -0.46, 0.88, 0.6) 0.05s;
  transform-origin: center;
}
.vjs-check-controller .vjs-check-controller-inner.is-radio {
  border-radius: 100%;
}
.vjs-check-controller .vjs-check-controller-inner.is-radio:after {
  border-radius: 100%;
  height: 4px;
  background-color: #fff;
  left: 50%;
  top: 50%;
}
.vjs-check-controller .vjs-check-controller-original {
  opacity: 0;
  outline: none;
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}
.vjs-carets {
  position: absolute;
  right: 0;
  cursor: pointer;
}
.vjs-carets svg {
  transition: transform 0.3s;
}
.vjs-carets:hover {
  color: #1890ff;
}
.vjs-carets-close {
  transform: rotate(-90deg);
}
.vjs-tree-node {
  display: flex;
  position: relative;
  line-height: 20px;
}
.vjs-tree-node.has-carets {
  padding-left: 15px;
}
.vjs-tree-node.has-carets.has-selector,
.vjs-tree-node.has-selector {
  padding-left: 30px;
}
.vjs-tree-node.is-highlight,
.vjs-tree-node:hover {
  background-color: #e6f7ff;
}
.vjs-tree-node .vjs-indent {
  display: flex;
  position: relative;
}
.vjs-tree-node .vjs-indent-unit {
  width: 1em;
}
.vjs-tree-node .vjs-indent-unit.has-line {
  border-left: 1px dashed #bfcbd9;
}
.vjs-tree-node.dark.is-highlight,
.vjs-tree-node.dark:hover {
  background-color: #2e4558;
}
.vjs-node-index {
  position: absolute;
  right: 100%;
  margin-right: 4px;
  user-select: none;
}
.vjs-colon {
  white-space: pre;
}
.vjs-comment {
  color: #bfcbd9;
}
.vjs-value {
  word-break: break-word;
}
.vjs-value-null,
.vjs-value-undefined {
  color: #d55fde;
}
.vjs-value-boolean,
.vjs-value-number {
  color: #1d8ce0;
}
.vjs-value-string {
  color: #008f2b;
}
.vjs-tree {
  font-family:
    Monaco,
    Menlo,
    Consolas,
    Bitstream Vera Sans Mono,
    monospace;
  font-size: 14px;
  text-align: left;
}
.vjs-tree.is-virtual {
  overflow: auto;
}
.vjs-tree.is-virtual .vjs-tree-node {
  white-space: nowrap;
}
