<template>
    <div class="q-gutter-y-md q-mt-md">
        <q-select v-model="selectedYear" :options="studentCodeYears" label="ปีการศึกษา" outlined input-debounce="0"
            @update:model-value="handleYearChange" style="width: 180px; margin-bottom: 20px" />
        <svg ref="legendSvgRef" height="20" style="position: sticky; top: 0; background: white; z-index: 10;">
        </svg>
        <div class="chart-container" :style="`max-height: ${availableHeight}px; overflow-y: auto; position: relative;`">
            <svg ref="chart" class="chart-wrapper"></svg>
            <svg ref="xAxisSvgRef" height="40" :width="width"
                style="position: sticky; bottom: 0; background: white;"></svg>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted, nextTick, watch } from 'vue';
import * as d3 from 'd3';
import { CurriculumService } from 'src/services/curriculums';
import { useCurriculumStore } from 'src/stores/curriculum';
import { useRoute, useRouter } from 'vue-router';
import useFilterStore from 'src/stores/filter-search';
import { api } from 'src/boot/axios';

interface LevelSummary {
    // level: number;
    category: string;
    count: number;
    studentIds: number[];
}

interface SummarySkill {
    skillName: string;
    skillId: number;
    domain: string;
    totalStudent: number;
    levelSummary: LevelSummary[];
}
type ProcessedDatum = {
    skill: string;
    totalStudent: number;
    [key: string]: string | number;
};
const props = defineProps<{
    skillType: string
}>()

// Initialize route and router first
const filterStore = useFilterStore();
const currentRoute = useRoute();
const currentRouter = useRouter();

const selectedYear = ref(currentRoute.query.year);

// Watch for route query parameter changes
watch(() => currentRoute.query.year, (newYear) => {
    if (newYear) {
        selectedYear.value = newYear;
        void fetchData(); // Refetch data when year changes
    }
});

// Watch for route name changes to clean up tooltips when navigating away
watch(() => currentRoute.name, () => {
    // Remove all tooltips when route changes
    d3.select('body').selectAll('.tooltip').remove();
});

// Handle year selection change
const handleYearChange = async (newYear: string) => {
    // Update URL query parameters with new year
    const currentQuery = { ...currentRoute.query };
    const yearNumber = newYear;
    currentQuery.year = yearNumber.toString();
    await currentRouter.replace({
        path: currentRoute.path,
        query: currentQuery
    });
};
const chart = ref<SVGSVGElement | null>(null);
const legendSvgRef = ref<SVGSVGElement | null>(null);
const xAxisSvgRef = ref<SVGSVGElement | null>(null);
const width = ref(800); // Default width
const dynamicHeight = ref(400);
const store = useCurriculumStore();
const currCode = computed(() => currentRoute.params.code as string);
const data = ref<SummarySkill[]>([]);
let lastDataHash = '';
let resizeObserver: ResizeObserver | null = null;
const availableHeight = ref(600) // default fallback
// Constants
const DEFAULT_WIDTH = 800;
const HEADER_HEIGHT = 300;

function getContainerWidth(): number {
    const container = chart.value?.parentElement;
    return container?.clientWidth || DEFAULT_WIDTH;
}
const studentCodeYears = ref<string[]>([]);
const fetchCodeYears = async (
    facultyId?: number,
    branchId?: number,
    curriculumId?: number,
) => {
    if (!filterStore.isShowFilter('codeYear')) return;
    const { data } = await api.get<string[]>('/students/code-year', {
        params: {
            facultyId,
            branchId,
            curriculumId,
        },
    });
    if (data) {
        studentCodeYears.value = data;
    }
};

const fetchData = async () => {
    if (!store.getInsertId) {
        await store.fetchOneByCode(currCode.value);
    }
    const insertId = store.getInsertId;
    if (insertId === undefined) return;

    // Handle selectedYear - it could be string, array, or null from route query
    let yearValue: string | undefined;
    if (selectedYear.value) {
        if (Array.isArray(selectedYear.value)) {
            const firstYear = selectedYear.value[0];
            yearValue = firstYear ? String(firstYear) : undefined;
        } else {
            yearValue = String(selectedYear.value);
        }
    }

    data.value = await CurriculumService.getSummary(insertId, yearValue, props.skillType);
    console.log('Fetched Data:', data.value)
    await nextTick();
    requestAnimationFrame(() => {
        void drawChart(data.value);
    });
};

const categories = ['Below Target', 'On Target', 'Above Target'];
const color = d3.scaleOrdinal<string, string>()
    .domain(['below target', 'on target', 'above target'])
    .range(['#FF9999', '#A2F0DA', '#6ECFF7']);
const categoryMap: Record<string, string> = {
    below: 'Below Target',
    on: 'On Target',
    above: 'Above Target'
};

const processedData = computed(() => {
    if (!data.value.length) return [];
    return data.value.map(d => {
        const total = d.totalStudent || 1;
        const summary: ProcessedDatum = {
            skill: d.skillName,
            totalStudent: total,
            skillId: d.skillId
        };
        categories.forEach(cat => {
            summary[cat] = 0;
        });
        d.levelSummary.forEach(ls => {
            const mappedCategory = categoryMap[ls.category];
            summary[mappedCategory] = ls.count;
        });
        categories.forEach(cat => {
            summary[cat] = ((summary[cat] as number) / total) * 100;
        });
        return summary;
    });
});


const drawChart = async (data: SummarySkill[]) => {
    if (!chart.value) return;
    const hash = JSON.stringify(data);
    if (hash === lastDataHash) return; // ข้อมูลเหมือนเดิม ไม่ต้องวาดใหม่
    lastDataHash = hash;

    await nextTick();
    width.value = getContainerWidth();
    const margin = { top: 4, right: 30, bottom: 10, left: 190 };
    const innerWidth = width.value - margin.left - margin.right;
    const calculatedHeight = data.length * 40 + margin.top + margin.bottom;
    dynamicHeight.value = calculatedHeight;
    console.log(processedData)

    console.log(availableHeight.value)
    const svg = d3.select(chart.value)
        .attr("viewBox", `0 0 ${width.value} ${dynamicHeight.value}`)
        .attr("preserveAspectRatio", "xMidYMid meet")
        .classed("responsive-svg", true);
    svg.selectAll('*').remove(); // Clear previous chart
    d3.select('body').selectAll('.tooltip').remove(); // Remove any existing tooltips

    const y = d3.scaleBand()
        .domain(processedData.value.map(d => d.skill))
        .range([margin.top, dynamicHeight.value - margin.bottom])
        .padding(0.4);

    const x = d3.scaleLinear()
        .domain([0, 100])
        .range([0, innerWidth]);

    const xAxis = d3.axisBottom(x)
        .tickValues([0, 50, 100])
        .tickSize(0)
        .tickFormat(d3.format(".2f"));

    const series: d3.Series<ProcessedDatum, string>[] = d3.stack<ProcessedDatum, string>()
        .keys(categories)(processedData.value);

    const g = svg.append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // แกน y 
    const yAxisGroup = g.append('g').call(d3.axisLeft(y));
    yAxisGroup.selectAll("path, line").attr("stroke", "#999").style("stroke-width", "1.3px");
    yAxisGroup.selectAll(".tick text").attr("fill", "#80828A").style("font-size", "13px").style('font-family', 'Sarabun');

    // แกน x
    const xAxisSvg = d3.select(xAxisSvgRef.value);
    xAxisSvg.selectAll("path").remove();
    xAxisSvg.selectAll("line").remove();
    xAxisSvg.selectAll(".tick text").remove();
    xAxisSvg.append('g')
        .attr('transform', `translate(${margin.left}, 10)`)
        .call(xAxis)
    xAxisSvg.selectAll(".tick text")
        .attr("fill", "#80828A")
        .style("font-size", "13px")
        .style('font-family', 'Sarabun');

    xAxisSvg.selectAll('.domain').attr('stroke', 'none') // เส้นหลักของแกน
    xAxisSvg.selectAll('.tick line').attr('stroke', 'none') // เส้น tick แต่ละอัน

    // grid lines
    g.select('.x-grid').remove();
    const xGrid = d3.axisBottom(x)
        .tickValues([0, 50, 100])
        .tickSize(-Math.floor(dynamicHeight.value)) // เส้นสูงเต็มพื้นที่กราฟ
        .tickFormat(() => '');  // ไม่ต้องมีตัวเลข
    g.append('g')
        .attr('class', 'x-grid')
        .attr('transform', `translate(0,${dynamicHeight.value})`)
        .call(xGrid);
    // grid
    g.select('.x-grid')
        .selectAll('line')
        .attr('stroke', '#ccc')
        .style("stroke-width", "1px")
        .attr('shape-rendering', 'crispEdges');

    g.select('.x-grid')
        .select('path')
        .remove(); // เอาเส้นแกน grid ทิ้งด้วย

    //hover
    const tooltip = d3.select('body').append('div')
        .attr('class', 'tooltip')
        .style('opacity', 0)
        .style('position', 'absolute')
        .style('background', '#fff')
        .style('padding', '10px')
        .style('border', 'none')
        .style('box-shadow', '0px 2px 8px rgba(0, 0, 0, 0.2)')
        .style('border-radius', '6px')
        .style('color', '#595757')
        .style('font-family', 'Sarabun')
        .style('min-width', '220px') //not responsive
        .style('max-width', '300px')
        .style('word-wrap', 'break-word')
        .style('pointer-events', 'none'); // Disable pointer events - tooltip can't be hovered

    // ก่อนวาดกราฟ สร้าง mapping จาก skill name → full data
    const originalDataMap = new Map<string, {
        skillName: string;
        totalStudent: number;
        levelSummary: { count: number; category: string; studentIds: number[] }[];
        skillId: number;
    }>();

    data.forEach(d => {
        originalDataMap.set(d.skillName, d);
    });

    const labelToKeyMap: Record<string, string> = {
        'Below Target': 'below',
        'On Target': 'on',
        'Above Target': 'above',
    };
    //วาดแท่งกราฟแบบซ้อน stackbar
    const groups = g.selectAll('g.layer')
        .data(series)
        .enter().append('g')
        .attr('class', 'layer')
        .attr('fill', d => color(d.key));

    // วาดแท่งกราฟ
    groups.selectAll('rect')
        .data(d => d.filter(entry => (entry[1] - entry[0]) > 0))
        .enter().append('rect')
        .attr('y', d => y(d.data.skill)! - 1)
        .attr('x', d => x(d[0]))
        .attr('width', 0)
        .attr('height', y.bandwidth() + 7)

        .on('mouseover', function (event: MouseEvent, d: d3.SeriesPoint<ProcessedDatum>) {
            const datum = d;

            d3.select(this)
                .transition()
                .duration(200)
                .attr('x', () => x(datum[0]) - 3)
                .attr('width', () => x(datum[1]) - x(datum[0]));

            // Get tooltip content data
            const category = (d3.select(this.parentNode as Element).datum() as { key: string }).key;
            const skill = d.data.skill;
            const categoryKey = labelToKeyMap[category];
            const original = originalDataMap.get(skill);
            if (!original) return;

            const filteredCount = original.levelSummary
                .filter(l => l.category === categoryKey)
                .map(l => {
                    const colorMap: Record<string, string> = {
                        below: '#FF9999',
                        on: '#A2F0DA',
                        above: '#6ECFF7'
                    };

                    const label = categoryMap[l.category];
                    const color = colorMap[l.category];

                    return `
      <div style="display: flex; align-items: center; justify-content: space-between; font-size: 14px; font-family: Sarabun; margin-bottom: 4px;">
        <div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${color}; margin-right: 8px;"></span>
          <span>${label} :</span>
        </div>
        <div style="display: flex; align-items: center;">
          <span style="text-align: right;">${l.count}</span>
          <span style="margin-left: 10px;">คน</span>
        </div>
      </div>
    `;
                })
                .join('');

            // Set tooltip content and show it
            tooltip.html(`
  <div style="font-weight: bold; font-size: 16px; font-family: Sarabun; margin-bottom: 6px;">
    ${original.skillName}
  </div>

  <div style="font-size: 14px; font-family: Sarabun;">
    ${filteredCount}
  </div>

  <div style="margin-top: 4px; font-size: 14px; font-family: Sarabun; display: flex; justify-content: space-between; color: #555;">
    <span>จำนวนนิสิตทั้งหมด :</span>
    <span style="text-align: right; flex: 1;">${original.totalStudent}</span>
    <span style="margin-left: 10px;">คน</span>
  </div>

  <div style="margin-top: 10px; padding: 6px 8px; font-size: 12px; font-family: Sarabun; color: #666; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #3674b5;">
    คลิกเพื่อดูรายละเอียด
  </div>
`)
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 120) + 'px') // Move tooltip much higher above cursor
                .style('z-index', '9999')
                .transition()
                .duration(200)
                .style('opacity', 1);
        })
        .on('mousemove', function (event: MouseEvent) {
            // Update tooltip position on mouse move
            tooltip
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 120) + 'px');
        })
        .on('click', function (_event: Event, d: d3.SeriesPoint<ProcessedDatum>) {
            // Handle click on chart bar - navigate to student list
            const skill = d.data.skill;
            const original = originalDataMap.get(skill);
            const category = (d3.select(this.parentNode as Element).datum() as { key: string }).key;
            const categoryKey = labelToKeyMap[category]

            // Hide tooltip immediately when clicking
            tooltip.transition().duration(0).style('opacity', 0);

            if (original) {
                console.log('Navigating to student list for skill:', original.skillName, 'skillId:', original.skillId);
                // Navigate to SummaryStudentList with skillId parameter
                void currentRouter.push({
                    name: 'SummaryStudentList',
                    params: {
                        code: currCode.value,
                        skillId: original.skillId.toString()
                    },
                    query: {
                        ...currentRoute.query, // Preserve existing query params like year
                        category: categoryKey // Pass the clicked category as well
                    }
                });
            }
        })
        .on('mouseout', function (_event: Event, d: d3.SeriesPoint<ProcessedDatum>) {
            const datum = d;

            d3.select(this)
                .transition()
                .duration(200)
                .attr('x', () => x(datum[0]))
                .attr('width', () => x(datum[1]) - x(datum[0]));

            // Hide tooltip immediately when mouse leaves chart bar
            tooltip.transition().duration(500).style('opacity', 0);
        })
        .transition()
        .duration(1000)
        .attr('width', d => x(d[1]) - x(d[0]));

    // วาดข้อความเปอร์เซ็นต์ลงในแท่ง
    groups.selectAll('text')
        .data(d => d.filter(entry => (entry[1] - entry[0]) > 0))
        .enter().append('text')
        .attr('x', d => x(d[0]) + (x(d[1]) - x(d[0])) / 2)
        .attr('y', d => y(d.data.skill)! + y.bandwidth() / 2)
        .attr('dy', '0.35em')
        .attr('text-anchor', 'middle')
        .attr('fill', '#595757')
        .style('font-size', '11px')
        .style('font-family', 'Sarabun')
        .text(d => {
            const percent = (d[1] - d[0]).toFixed(2);
            return `${percent}%`;
        });
    const maxSpacing = 150; // max ระยะห่างแบบเดิม (ถ้ากว้างมาก)
    const spacing = Math.min(width.value / categories.length, maxSpacing);

    const legendSvg = d3.select(legendSvgRef.value);
    legendSvg.selectAll('*').remove();
    legendSvg
        .attr("width", width.value) // กำหนดให้เท่ากับกราฟหลัก
        .attr("height", 30)
        .attr("viewBox", `0 0 ${width.value} 30`)
        .attr("preserveAspectRatio", "xMidYMid meet");
    const legend = legendSvg.append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);

    categories.forEach((cat, i) => {
        const legendRow = legend.append('g')
            .attr('transform', `translate(${i * spacing}, 0)`);

        legendRow.append('rect')
            .attr('width', 10)
            .attr('height', 10)
            .attr('fill', color(cat));

        legendRow.append('text')
            .attr('x', 15)
            .attr('y', 10)
            .attr('text-anchor', 'start')
            .style('text-transform', 'capitalize')
            .attr('fill', '#595757')
            .style('font-family', 'Sarabun')
            .text(cat);
    });
};


// Initialize chart on mount
onMounted(async () => {
    availableHeight.value = window.innerHeight - HEADER_HEIGHT;
    filterStore.setPage('student_curriculum');
    // Ensure we have curriculum data first
    if (!store.getInsertId) {
        await store.fetchOneByCode(currCode.value);
    }

    await fetchCodeYears(undefined, undefined, store.getInsertId);
    await fetchData();
    // Set up single ResizeObserver for container width changes
    if (chart.value?.parentElement) {
        resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const rect = entry.contentRect;
                width.value = rect.width;
                if (data.value.length > 0) {
                    void drawChart(data.value);
                }
            }
        });
        resizeObserver.observe(chart.value.parentElement);
    }
});

onUnmounted(() => {
    resizeObserver?.disconnect(); // cleanup
    // Remove all tooltips when component is unmounted
    d3.select('body').selectAll('.tooltip').remove();
    // Note: We intentionally don't clear localStorage here to persist the selection
});

</script>

<style scoped>
.chart-wrapper {
    width: 100%;
    height: auto;
    display: block;
}

.chart-wrapper svg g.legend {
    position: sticky;
    top: 0;
    background-color: white;
    /* ป้องกันโปร่ง */
    z-index: 10;
}

.chart-wrapper svg g.x-axis-sticky {
    position: sticky;
    bottom: 0;
    background-color: white;
    z-index: 9;
}

.responsive-svg {
    width: 100%;
    height: 100%;
}

.chart-container {
    max-height: calc(100vh - 260px);
    overflow-y: auto;
    position: relative;
    width: 100%;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE & Edge */
    /* หรือจะใช้ 4/3, 1/1 ก็ได้ */
}

.chart-container::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari */
}
</style>
