<template>
  <PageTitle class="q-mb-md" :title="t('coordinatorsManagement')" />
  <PageHeader
    v-model:search-text="filterStore.search"
    @open-dialog="store.toggleDialog"
    hide-filter
    hide-search
  />
  <!-- Main Table -->
  <q-table
    flat
    bordered
    :pagination="store.pagination"
    class="q-animate--fade q-mt-lg"
    separator="cell"
    :rows="store.getCoordinators"
    row-key="thaiName"
    :loading="global.getLoadingState"
    :columns="coordinatorColumns"
    :filter="filterStore.search"
    @update:pagination="store.fetchAllInCurr"
    @request="(r) => store.fetchAllInCurr(r.pagination)"
  >
    <template #body-cell-number="props">
      <q-td>
        {{ props.rowIndex + 1 }}
      </q-td>
    </template>
    <template #body-cell-actions="props">
      <q-td>
        <q-btn
          flat
          dense
          round
          color="grey-8"
          icon="delete"
          class="q-ml-sm"
          @click="
            store.removeAssignedInstructor({
              curriculumId: curr.getInsertId!,
              instructorId: props.row.id,
            })
          "
        />
      </q-td>
    </template>
  </q-table>
  <!-- Dialog Assign Instructor -->
  <q-dialog v-model="store.dialogState">
    <q-card class="q-pa-lg" style="min-width: 70%">
      <div class="text-h5 text-weight-medium q-py-md text-primary">
        {{ t('assignCoordinator') }}
      </div>
      <q-separator class="full-width bg-primary q-mb-md" style="height: 2px" />
      <q-table
        flat
        bordered
        row-key="thaiName"
        separator="cell"
        :rows="store.getAvailableInstructors"
        :columns="coordinatorColumns"
        :pagination
        @update:pagination="store.fetchAvailableInstructors"
        @request="(p) => store.fetchAvailableInstructors(p.pagination)"
      >
        <template #body-cell-number="props">
          <q-td>
            {{ props.rowIndex + 1 }}
          </q-td>
        </template>
        <template #body-cell-actions="props">
          <q-td style="min-width: 150px">
            <q-btn
              size="md"
              unelevated
              color="primary"
              @click="
                store.assignInstructor({
                  curriculumId: curr.getInsertId!,
                  instructorId: props.row.id,
                })
              "
            >
              <q-icon name="add" class="q-mr-sm"></q-icon>
              <span>{{ t('assign') }}</span>
            </q-btn>
          </q-td>
        </template>
      </q-table>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
/*
    imports
*/
import type { QTable } from 'quasar';
import { useMeta } from 'quasar';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useInstructorStore } from 'src/stores/instructor';
import { useGlobalStore } from 'src/stores/global';
import PageHeader from 'src/components/common/PageHeader.vue';
import { useI18n } from 'vue-i18n';
import { useCurriculumStore } from 'src/stores/curriculum';
import PageTitle from 'src/components/common/PageTitle.vue';
import useFilterStore from 'src/stores/filter-search';
import { coordinatorColumns } from 'src/data/column_table';
/*
    states
*/
const pagination = ref<QTable['pagination']>();
const { t } = useI18n();
const global = useGlobalStore();
const store = useInstructorStore();
const route = useRoute();
const curr = useCurriculumStore();
const filterStore = useFilterStore();

const title = computed(() => route.matched[1]?.name as string);

useMeta({
  title: title.value,
});
</script>
