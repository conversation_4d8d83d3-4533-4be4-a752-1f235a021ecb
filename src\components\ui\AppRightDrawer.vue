<template>
  <q-drawer v-model="app.rightDrawerOpen" :width="250" side="right" bordered>
    <q-list>
      <q-item>
        <q-item-section v-if="app.currentTab" class="text-capitalize">
          {{ t(app.currentTab) }}
        </q-item-section>
        <q-item-section side>
          <q-btn
            padding="none"
            icon="close"
            flat
            @click="app.toggleRightDrawer()"
          ></q-btn>
        </q-item-section>
      </q-item>
      <q-separator />
      <q-item class="btn-menu">
        <q-toggle v-model="darkRef" :icon="getThemeIcon">
          {{ t('darkMode') }}
          <q-badge color="secondary">beta</q-badge>
        </q-toggle>
      </q-item>
      <q-separator></q-separator>
      <q-item class="btn-menu" clickable @click="changeLocale">
        <q-item-section side> <q-icon name="language"></q-icon></q-item-section>
        <q-item-section>
          {{ t('language') }} : {{ getCurrentLocale }}</q-item-section
        >
      </q-item>
    </q-list>
  </q-drawer>
</template>

<script lang="ts" setup>
import { LocalStorage, useQuasar } from 'quasar';
import { thaiQuasarLang } from 'src/boot/i18n';
import { useGlobalStore } from 'src/stores/global';
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const app = useGlobalStore();
const { dark } = useQuasar();
const darkRef = ref(false);

watch(
  () => darkRef.value,
  (val) => {
    dark.set(val);
    LocalStorage.set('theme', val ? 'dark' : 'light');
  },
);
const { t, locale } = useI18n();
const $q = useQuasar();

const getThemeIcon = computed(() =>
  dark.isActive ? 'dark_mode' : 'light_mode',
);

const getCurrentLocale = computed(() => {
  const cur = locale.value.split('-')[0];
  if (cur === 'th') {
    return 'TH';
  } else {
    return 'EN';
  }
});

async function changeLocale() {
  if (locale.value === 'en-US') {
    locale.value = 'th-TH';
    $q.lang.set(thaiQuasarLang);
  } else {
    locale.value = 'en-US';
    const moduleEn = import.meta.glob(
      '../../../node_modules/quasar/lang/(en-US).js',
    );
    await moduleEn[`../../../node_modules/quasar/lang/en-US.js`]()
      .then((lang: unknown) => {
        //@ts-expect-error lang not found
        $q.lang.set(lang.default);
      })
      .catch((err) => {
        throw err;
      });
  }
}
</script>
