<template>
  <q-page class="q-pa-md background-pattern">
    <div
      class="header-container-line"
      style="
        padding: 20px;
        margin-top: -16px;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
      "
    ></div>
    <div class="header-bg">
      <div class="header-container">
        <div class="logo-container">
          <q-img
            src="logos/buu-og.png"
            alt="logo"
            height="160px"
            width="160px"
            fit="contain"
            class="buu-logo"
          />
        </div>
        <div class="title-container">
          <h2 class="title">แบบประเมินทักษะนิสิตสหกิจศึกษา/ฝึกงาน</h2>
          <h6 class="subtitle">
            วิทยาศาสตรบัณฑิต {{ branch?.thaiName || '่ไม่มีข้อมูล' }}<br />
            Bachelor of Science {{ branch?.engName || '่ไม่มีข้อมูล' }}
          </h6>
          <h5 class="faculty">คณะวิทยาการสารสนเทศ | Faculty of Informatics</h5>
          <h5 class="faculty" style="margin-top: -20px">
            มหาวิทยาลัยบูรพา | Burapha University
          </h5>
        </div>
        <div class="student-photo-wrapper">
          <q-img
            class="graduate-photo"
            :src="`https://reg.buu.ac.th/registrar/getstudentimage.asp?id=${studentCode}`"
            alt="Graduate"
            height="220px"
            width="170px"
            fit="cover"
          >
            <template #loading></template>
          </q-img>
        </div>
      </div>
    </div>

    <div
      class="header-container-line"
      style="width: 100vw; margin-left: calc(-50vw + 50%); padding: 10px"
    ></div>

    <div class="student-info" style="margin-top: 50px">
      <div class="student-details-grid">
        <div class="student-col">
          <strong>รหัสนิสิต :</strong> {{ studentCode }}
        </div>
        <div class="student-col">
          <strong>ชื่อ:</strong> {{ studentData.thaiName }}
        </div>
        <div class="student-col"><strong>ปีการศึกษา:</strong> 2024</div>
      </div>
      <div class="student-details-grid">
        <div class="student-col">
          <strong>Student ID:</strong> {{ studentCode }}
        </div>
        <div class="student-col">
          <strong>Name:</strong> {{ studentData.engName }}
        </div>
        <div class="student-col"><strong>Academic Year:</strong> 2024</div>
      </div>
    </div>

    <q-separator inset style="width: 90%; margin: 3% auto" />

    <div style="margin-top: 50px; width: 90%; margin: 3% auto">
      <div class="student-col" style="font-size: 18px">
        <strong>บริษัท :</strong> NASA (The National Aeronautics and Space
        Administration)
      </div>
      <div class="student-col" style="font-size: 18px; margin-top: 2%">
        <strong>ตำแหน่งงานที่ฝึก :</strong> นักบินอวกาศ
      </div>
      <div class="student-col" style="font-size: 18px; margin-top: 2%">
        <strong>ระยะเวลาฝึกงาน :</strong> 4 เดือน
      </div>
    </div>
    <q-separator inset style="width: 90%; margin: 3% auto" />
    <div class="q-mt-lg" style="width: 90%; margin: 3% auto">
      <div class="text-h6">1. แบบประเมินทักษะนิสิตสหกิจศึกษา/ฝึกงาน</div>
      <q-markup-table flat class="q-mt-lg">
        <thead style="background-color: #1a237e; color: white">
          <tr>
            <th class="text-left">No.</th>
            <th class="text-left">Skill Name</th>
            <th class="text-left">Level from University</th>
            <th class="text-left">Level from Company</th>
            <th class="text-left" style="max-width: 150px; white-space: nowrap">
              Remarks
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td
              colspan="5"
              class="q-pa-sm bg-blue-1 text-primary text-weight-bold"
              style="height: 48px; font-size: 16px"
            >
              Specific Skills
            </td>
          </tr>
          <template
            v-for="(skill, index) in specificSkills"
            :key="'specific-' + skill.id"
          >
            <tr>
              <td>{{ index + 1 }}</td>
              <td>{{ skill.name }}</td>
              <td v-if="getSubskills(skill.id).length === 0">
                {{ Math.min(skill.score ?? 0, 5) }}&nbsp;&nbsp;&nbsp;&nbsp;({{
                  skill.level
                }})
              </td>
              <td v-else></td>

              <td>
                <q-select
                  v-if="getSubskills(skill.id).length === 0"
                  v-model="companyLevelsspecificSkills[skill.id]"
                  :options="companyLevelspecificSkillsOptions"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="min-width: 150px"
                />
              </td>
              <td
                v-if="getSubskills(skill.id).length === 0"
                style="
                  max-width: 150px;
                  width: 150px;
                  white-space: nowrap;
                  overflow: hidden;
                "
              >
                <q-select
                  v-model="companyspecificSkillRemarks[skill.id]"
                  :options="Remarks"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="width: 100%; max-width: 150px"
                />
              </td>
              <td v-else style="max-width: 150px; width: 150px"></td>
            </tr>

            <!-- แถว subskill -->
            <tr v-for="sub in getSubskills(skill.id)" :key="'sub-' + sub.id">
              <td></td>
              <td class="text-grey-8 q-pl-md">└ {{ sub.name }}</td>
              <td>
                {{ Math.min(sub.gained ?? 0, 5) }}&nbsp;&nbsp;&nbsp;&nbsp;({{
                  mapScoreToLevel(sub.gained)
                }})
              </td>
              <td>
                <q-select
                  v-model="companyLevelsspecificSkills[sub.id]"
                  :options="companyLevelspecificSkillsOptions"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="min-width: 150px"
                />
              </td>
              <td style="max-width: 150px; width: 150px; white-space: nowrap">
                <q-select
                  v-model="companyspecificSkillRemarks[sub.id]"
                  :options="Remarks"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="width: 100%"
                />
              </td>
            </tr>
          </template>

          <tr>
            <td
              colspan="5"
              class="q-pa-sm bg-green-1 text-positive text-weight-bold"
              style="height: 48px; font-size: 16px"
            >
              Soft Skills
            </td>
          </tr>
          <template
            v-for="(skill, index) in softSkills"
            :key="'soft-' + skill.id"
          >
            <tr>
              <td>{{ index + 1 }}</td>
              <td>{{ skill.name }}</td>
              <td v-if="getSubskills(skill.id).length === 0">
                {{ Math.min(skill.score ?? 0, 5) }}&nbsp;&nbsp;&nbsp;&nbsp;({{
                  skill.level
                }})
              </td>
              <td v-else></td>
              <td>
                <q-select
                  v-if="getSubskills(skill.id).length === 0"
                  v-model="companyLevelssoftSkills[skill.id]"
                  :options="companyLevelsoftSkillOptions"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="min-width: 150px"
                />
              </td>
              <td style="max-width: 150px; width: 150px; white-space: nowrap">
                <q-select
                  v-if="getSubskills(skill.id).length === 0"
                  v-model="companysoftSkillRemarks[skill.id]"
                  :options="Remarks"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="width: 100%"
                />
              </td>
            </tr>

            <tr
              v-for="sub in getSubskills(skill.id)"
              :key="'soft-sub-' + sub.id"
            >
              <td></td>
              <td class="text-grey-8 q-pl-md">└ {{ sub.name }}</td>
              <td>
                {{ Math.min(sub.gained ?? 0, 3) }}&nbsp;&nbsp;&nbsp;&nbsp;({{
                  mapSoftScoreToLevel(sub.gained)
                }})
              </td>
              <td>
                <q-select
                  v-model="companyLevelssoftSkills[sub.id]"
                  :options="companyLevelsoftSkillOptions"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="min-width: 150px"
                />
              </td>
              <td style="max-width: 150px; width: 150px; white-space: nowrap">
                <q-select
                  v-model="companysoftSkillRemarks[sub.id]"
                  :options="Remarks"
                  outlined
                  dense
                  emit-value
                  map-options
                  style="width: 100%"
                />
              </td>
            </tr>
          </template>
        </tbody>
      </q-markup-table>

      <q-separator inset style="width: 100%; margin: 5% auto" />

      <div style="font-size: 18px; margin-top: 2%">
        <div class="text-h6 q-mb-sm">2. พฤติกรรมในการทำงาน</div>
        <div class="q-gutter-md" style="margin-left: 1%">
          <div
            v-for="(item, index) in workBehaviors"
            :key="index"
            style="margin-top: 2%"
          >
            <div class="q-mb-xs">2.{{ index + 1 }}. {{ item.label }}</div>
            <q-option-group
              v-model="behaviorScores[item.key]"
              :options="ratingScale"
              type="radio"
              color="primary"
              inline
            />
          </div>
        </div>
      </div>
      <q-separator inset style="width: 100%; margin: 5% auto" />

      <div style="font-size: 18px; margin-top: 2%">
        <div class="text-h6 q-mb-sm">3. สรุปผลการประเมิน</div>
        <q-option-group
          v-model="evaluationResult"
          :options="[
            { label: 'ผ่าน', value: 'pass' },
            { label: 'ควรพัฒนาเพิ่มเติม', value: 'improve' },
            { label: 'ไม่ผ่าน', value: 'fail' },
          ]"
          type="radio"
          color="primary"
          inline
        />
      </div>
      <q-separator inset style="width: 100%; margin: 5% auto" />

      <div style="font-size: 18px; margin-top: 2%">
        <div class="text-h6 q-mb-sm">
          4. ข้อเสนอแนะเพิ่มเติมจากสถานประกอบการ
        </div>
        <q-input
          v-model="companyFeedback"
          type="textarea"
          autogrow
          outlined
          placeholder="กรุณากรอกข้อเสนอแนะ หรือความคิดเห็นเพิ่มเติม..."
          rows="4"
        />
      </div>
      <q-separator inset style="width: 100%; margin: 5% auto" />

      <div style="font-size: 18px; margin-top: 2%">
        <div class="text-h6 q-mb-sm">5. ความเหมาะสมในการจ้างงานในอนาคต</div>
        <q-option-group
          v-model="futureEmploymentSuitability"
          :options="[
            { label: 'เหมาะสมมาก', value: 'excellent' },
            { label: 'เหมาะสม', value: 'good' },
            { label: 'ไม่แน่ใจ', value: 'uncertain' },
            { label: 'ไม่เหมาะสม', value: 'unsuitable' },
          ]"
          type="radio"
          color="primary"
          inline
          class="q-mb-md"
        />

        <q-input
          v-model="futureEmploymentRemark"
          type="textarea"
          autogrow
          outlined
          placeholder="โปรดระบุเหตุผลประกอบ (ถ้ามี)..."
          rows="3"
        />
      </div>
      <q-separator inset style="width: 100%; margin: 5% auto" />

      <div style="font-size: 18px; margin-top: 2%">
        <div class="text-h6 q-mb-sm">6. ข้อมูลผู้ประเมิน</div>

        <div class="q-gutter-sm">
          <div style="font-size: 18px; margin-top: 2%">
            <q-input
              v-model="evaluatorName"
              autogrow
              outlined
              placeholder="กรุณากรอกชื่อผู้ประเมิน"
              rows="4"
            />
          </div>
          <div style="font-size: 18px; margin-top: 2%">
            <q-input
              v-model="evaluatorPosition"
              autogrow
              outlined
              placeholder="กรุณากรอกตำแหน่ง"
              rows="4"
            />
          </div>
        </div>
      </div>
      <div class="row justify-end q-mt-md" style="margin-top: 2%">
        <q-btn
          v-if="!isExporting"
          label="Download PDF"
          color="primary"
          style="margin-right: 1%"
          @click="downloadPDF"
        />
        <q-btn label="ส่งแบบประเมิน" color="green" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { SkillCollectionService } from 'src/services/skill-collections';
import { StudentService } from 'src/services/student';
import { useRoute } from 'vue-router';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useRouter } from 'vue-router';
import { BranchService } from 'src/services/branch';
import type { Branch, Student } from 'src/types/education';

const router = useRouter();

export interface SkillItem {
  id: number;
  name: string;
  domain: string;
  gained: number;
  subskills: SkillItem[];
}
interface DisplaySkill {
  id: number;
  name: string;
  level: string;
  score?: number;
}

const branch = ref<Branch | null>(null);
const topSpecificSkills = ref<DisplaySkill[]>([]);
const topSoftSkills = ref<DisplaySkill[]>([]);
const isExporting = ref(false);
const data = ref<{ specific: SkillItem[]; soft: SkillItem[] }>();
const studentData = ref<Student>({} as Student);
const allSkills = ref<SkillItem[]>([]);
const specificSkills = ref<DisplaySkill[]>([]);
const softSkills = ref<DisplaySkill[]>([]);
const route = useRoute();
const studentCode = computed(() => String(route.params.code));
const companyFeedback = ref('');
const evaluatorName = ref('');
const evaluatorPosition = ref('');
const evaluationResult = ref<string>('');
const futureEmploymentSuitability = ref<string>('');
const futureEmploymentRemark = ref<string>('');

const ratingScale = [
  { label: '1 - ควรปรับปรุงมาก', value: 1 },
  { label: '2 - ควรปรับปรุง', value: 2 },
  { label: '3 - พอใช้', value: 3 },
  { label: '4 - ดี', value: 4 },
  { label: '5 - ดีมาก', value: 5 },
];

const workBehaviors = [
  { label: 'ตรงต่อเวลา / วินัย', key: 'punctuality' },
  { label: 'รับผิดชอบต่องานที่ได้รับมอบหมาย', key: 'responsibility' },
  { label: 'เปิดใจรับคำแนะนำ', key: 'openMindedness' },
  { label: 'ความกระตือรือร้นในการเรียนรู้', key: 'enthusiasm' },
  { label: 'ปฏิบัติตามระเบียบของบริษัท', key: 'followRules' },
];

const behaviorScores = ref<Record<string, number>>({});

watch(
  () => route.params.code,
  (newCode, oldCode) => {
    if (newCode !== oldCode) {
      window.location.reload();
    }
  },
);

const loadTranscript = async () => {
  try {
    data.value = await SkillCollectionService.fetchTranscript(
      studentCode.value,
    );
    studentData.value = await StudentService.getOne(studentCode.value);
    if (studentData.value.branchId) {
      branch.value = await BranchService.getOne(studentData.value.branchId);
    }

    allSkills.value.push(...(data.value?.specific || []));
    allSkills.value.push(...(data.value?.soft || []));

    specificSkills.value =
      data.value?.specific.map((item) => ({
        id: item.id,
        name: item.name,
        level: mapScoreToLevel(item.gained),
        score: item.gained,
      })) || [];

    // remove score 0
    specificSkills.value = specificSkills.value.filter(
      (skill) => skill.score !== 0,
    );
    console.log('specificSkills.value', specificSkills.value);

    softSkills.value =
      data.value?.soft.map((item) => ({
        id: item.id,
        name: item.name,
        level: mapSoftScoreToLevel(item.gained),
        score: item.gained,
      })) || [];

    // remove score 0
    softSkills.value = softSkills.value.filter((skill) => skill.score !== 0);

    if (!data.value || !studentData.value || !studentData.value.code) {
      throw new Error('Invalid student data');
    }
  } catch (error) {
    console.error('ไม่พบข้อมูลนิสิต หรือเกิดข้อผิดพลาด:', error);
    void router.replace('/error-not-found');
  }
};

onMounted(async () => {
  await loadTranscript();
});

function mapScoreToLevel(score: number): string {
  if (score >= 5) return 'Master';
  if (score === 4) return 'Expert';
  if (score === 3) return 'Competent';
  if (score === 2) return 'Practitioner';
  return 'Observe';
}

function mapSoftScoreToLevel(score: number): string {
  if (score >= 3) return 'Exemplary';
  if (score === 2) return 'Established';
  return 'Emerging';
}

const downloadPDF = async () => {
  isExporting.value = true;

  topSpecificSkills.value = [...specificSkills.value]
    .sort((a, b) => (b.score ?? 0) - (a.score ?? 0))
    .slice(0, 8);

  topSoftSkills.value = [...softSkills.value]
    .sort((a, b) => (b.score ?? 0) - (a.score ?? 0))
    .slice(0, 5);

  const originalSpecific = [...specificSkills.value];
  const originalSoft = [...softSkills.value];

  specificSkills.value = [...topSpecificSkills.value];
  softSkills.value = [...topSoftSkills.value];

  await nextTick();
  await new Promise((r) => setTimeout(r, 300));

  const target = document.querySelector('.q-page') as HTMLElement;
  if (!target) return;

  const canvas = await html2canvas(target, {
    scale: 3,
    useCORS: true,
  });

  const imgData = canvas.toDataURL('image/png');
  const pdf = new jsPDF('p', 'mm', 'a4');

  const imgProps = pdf.getImageProperties(imgData);
  const pdfWidth = pdf.internal.pageSize.getWidth();
  const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

  const marginY = 0;
  const scaleUp = 1.2;

  pdf.addImage(
    imgData,
    'PNG',
    -((pdfWidth * (scaleUp - 1)) / 2),
    marginY,
    pdfWidth * scaleUp,
    pdfHeight * scaleUp,
  );

  pdf.save(`transcript-${studentCode.value}.pdf`);

  specificSkills.value = [...originalSpecific];
  softSkills.value = [...originalSoft];
  isExporting.value = false;
};

const companyLevelsspecificSkills = ref<Record<number, string>>({});
const companyspecificSkillRemarks = ref<Record<number, string>>({});
const companyLevelssoftSkills = ref<Record<number, string>>({});
const companysoftSkillRemarks = ref<Record<number, string>>({});
const nbsp = '\u00A0';
const companyLevelspecificSkillsOptions = [
  { label: `1${nbsp}${nbsp}${nbsp}${nbsp}(Observe)`, value: 'Observe' },
  {
    label: `2${nbsp}${nbsp}${nbsp}${nbsp}(Practitioner)`,
    value: 'Practitioner',
  },
  { label: `3${nbsp}${nbsp}${nbsp}${nbsp}(Competent)`, value: 'Competent' },
  { label: `4${nbsp}${nbsp}${nbsp}${nbsp}(Expert)`, value: 'Expert' },
  { label: `5${nbsp}${nbsp}${nbsp}${nbsp}(Master)`, value: 'Master' },
  { label: 'N/A', value: 'N/A' },
];

const companyLevelsoftSkillOptions = [
  { label: `1${nbsp}${nbsp}${nbsp}${nbsp}(Emerging)`, value: 'Emerging' },
  { label: `2${nbsp}${nbsp}${nbsp}${nbsp}(Established)`, value: 'Established' },
  { label: `3${nbsp}${nbsp}${nbsp}${nbsp}(Exemplary)`, value: 'Exemplary' },
  { label: 'N/A', value: 'N/A' },
];

const Remarks = [
  { label: 'Pass', value: 'Pass' },
  { label: 'Fail', value: 'Fail' },

  { label: 'N/A', value: 'N/A' },
];

function getSubskills(skillId: number): SkillItem[] {
  return allSkills.value.find((s) => s.id === skillId)?.subskills || [];
}
</script>

<style scoped>
.q-page {
  color: #1a237e;
  background: #fff;
}
.full-width-line {
  width: 100vw;
  height: 8px;
  background: linear-gradient(120deg, #1e3a8a, #2563eb);
  margin-left: calc(-50vw + 50%);
  margin-top: -16px;
}

.background-pattern {
  background-size: cover;
}

.header-bg {
  width: 100vw;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
}

.header-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  gap: 24px;
  width: 90%;
  max-width: 1400px;
  background-color: #ffffff;
}

.student-info,
.skills-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  gap: 24px;
  margin: 30px auto;
  width: 80%;
  margin-top: 5%;
}
.header-container-line {
  background: linear-gradient(120deg, #1e3a8a, #2563eb);
  height: 20px;
  width: 100%;
}
.logo-container,
.student-photo-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
}
.buu-logo {
  border-radius: 50%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  background: radial-gradient(circle, #ffffff, #e2e8f0);
  padding: 8px;
  transition: transform 0.3s ease;
}

.title-container {
  flex: 2;
  text-align: center;
  margin-top: -20px;
}
.title {
  font-size: 35px;
  font-weight: bold;
  margin-bottom: 12px;
}
.subtitle {
  font-size: 14px;
  margin-top: 6px;
  line-height: 1.4;
}
.faculty {
  font-size: 18px;
  font-weight: bold;
  margin-top: 4px;
}
.graduate-photo {
  border-radius: 12px;
  border: 4px solid white;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
.student-details {
  font-size: 16px;
  color: #1e293b;
  line-height: 1.6;
  text-align: center;
  margin-top: 10px;
}
.chart-box {
  flex: 1;
  min-width: 280px;
  max-width: 400px;
  text-align: center;
  height: 420px;
}
.skill-list {
  flex: 2;
  min-width: 300px;
  margin-left: 10%;
  height: 420px;
}
.skill-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  padding-top: 10px;
  max-height: 360px;
  overflow-y: auto;
  padding-right: 8px;
}

.skill-card {
  padding: 16px 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 160px;
  flex: 1 1 calc(33% - 16px);
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}
.skill-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
}
.skill-card.soft {
  background: linear-gradient(145deg, #dcffe1, #ffffff);
  border: 1px solid #a5e6c4;
  box-shadow: 0 4px 14px rgba(0, 128, 96, 0.1);
}
.skill-card.specific {
  background: linear-gradient(145deg, #e5ecf5, #ffffff);
  border: 1px solid #acc8ec;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
}
.skill-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 6px;
  display: block;
}
.skill-name.specific {
  color: #1a237e;
}
.skill-name.soft {
  color: #0f7a01;
}
.skill-achievement {
  font-size: 14px;
  color: #475569;
}
.chart-title {
  color: #000000;
  font-weight: bold;
  margin-bottom: 12px;
  margin-top: 10px;
}
.skill-levels,
.english-level {
  text-align: center;
  font-weight: bold;
  margin-top: 50px;
  color: #1e293b;
}
@media (max-width: 768px) {
  .skill-card {
    flex: 1 1 100%;
  }
  .title {
    font-size: 28px;
  }
}

.sub-skill-card {
  padding: 16px 20px;
  border-radius: 12px;
  min-width: 160px;
  flex: 1 1 calc(33% - 16px);
  background: linear-gradient(145deg, #e5ecf5, #ffffff);
  border: 1px solid #acc8ec;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
}

.student-details-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px 80px;
  font-size: 18px;
  color: #1e293b;
  margin: auto;
  text-align: left;
}
.student-col strong {
  color: #1e40af;
}

.final-verification {
  margin-top: 60px;
  gap: 32px;
}

.skill-levels.note {
  font-size: 14px;
  margin-top: 10px;
  color: #64748b;
  text-align: center;
}
.relative-position {
  position: relative;
}

.q-markup-table tbody tr:nth-child(even),
.q-markup-table tbody tr:nth-child(odd) {
  background-color: white !important;
}
</style>
