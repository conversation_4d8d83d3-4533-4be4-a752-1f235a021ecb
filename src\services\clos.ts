import { api } from 'boot/axios';
import type { DataResponse, QueryParams } from 'src/types/api';
import type { CLO } from 'src/types/education';
export class ClosService {
  static path = 'clos';
  static async getAll(p?: Partial<QueryParams>) {
    const res = await api.get<DataResponse<CLO>>(this.path, { params: p });
    return res.data;
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`);
    return res.data;
  }
  static async getAllBySubject(id: number) {
    const res = await api.get<DataResponse<CLO>>(`${this.path}`, {
      params: { subjectId: id },
    });
    return res.data;
  }

  static async createOne(obj: Partial<CLO>) {
    const dto = {
      name: obj.name,
      subjectId: obj.subjectId,
      thaiDescription: obj.thaiDescription,
      engDescription: obj.engDescription,
      expectSkillLevel: obj.expectSkillLevel,
      ploId: obj.ploId || obj.plo?.id,
      skillId: obj.skillId || obj.skill?.id,
    } as Partial<CLO>;
    const res = await api.post(this.path, dto);
    return res.data;
  }

  static async updateOne(obj: Partial<CLO>): Promise<CLO> {
    const dto = {
      id: obj.id,
      name: obj.name,
      thaiDescription: obj.thaiDescription,
      engDescription: obj.engDescription,
      expectSkillLevel: obj.expectSkillLevel,
      ploId: obj.ploId,
      skillId: obj.skillId,
    } as Partial<CLO>;
    const res = await api.patch(`${this.path}/${obj.id}`, dto);
    return res.data;
  }

  static async removeOne(id: number) {
    const res = await api.delete(`${this.path}/${id}`);
    return res.data;
  }
}
