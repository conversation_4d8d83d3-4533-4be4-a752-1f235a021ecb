import { route } from 'quasar/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';

import routes from './routes';
import { useAuthStore } from 'src/stores/auth';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
      ? createWebHistory
      : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  Router.beforeEach((to, from, next) => {
    const { meta, path } = to;
    const isPublic = meta?.public || false;
    const allowedRoles = (meta?.roles as string[]) || [];

    try {
      const auth = useAuthStore();
      auth.loadUserFromSession();
      const userRole = auth.getRole;

      // Bypass authentication check for development
      // const isAuthenticated = true;
      // const userRole = EnumUserRole.ADMIN;

      // * Handle public routes first
      if (isPublic && path !== '/') {
        return next(); // Allow access to public routes
      }

      // * Handle '/' route, redirect based on user role
      if (path === '/') {
        const redirectPath = userRole ? '/dashboard' : '/landing';
        return next(redirectPath);
      }

      // * Redirect authenticated users away from the login page
      if (path === '/login' && userRole) {
        return next('/');
      }

      // * Redirect unauthenticated users from protected routes
      if (!isPublic && !userRole) {
        return next('/login');
      }

      // * Check if user role is allowed for this route
      if (
        allowedRoles.length > 0 &&
        userRole &&
        !allowedRoles.includes(userRole)
      ) {
        return next('/forbidden');
      }

      // * Proceed to the route
      next();
    } catch (error) {
      console.error('Error during route guard execution:', error);

      // * Ensure not to redirect to `/error` if already there
      if (path !== '/error') {
        return next('/error');
      }

      // * Proceed to `/error` route without recursion
      next();
    }
  });

  return Router;
});
