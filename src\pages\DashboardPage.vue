<template>
  <q-page padding>
    <div class="text-h4 text-weight-medium">
      {{ t('hello') }} {{ user.payload?.user.email }} 👋
    </div>
    <q-space class="q-my-md" />
    <section name="courses">
      <div class="text-h6 q-mb-lg text-weight-medium">
        {{ t('quickStart') }} 🚀
      </div>
      <div class="row q-gutter-y-lg full-width">
        <RouterLink
          v-for="card in homeCards"
          :key="card.headText"
          :to="card.goTo"
        >
          <CustomCard
            class="q-mr-lg"
            :head-text="card.headText"
            :sub-text="card.subText"
            hide-options
          />
        </RouterLink>
        <CustomCard
          class="cursor-pointer"
          @click="clickOnTranscript"
          :head-text="t('transcript')"
          :sub-text="`ต้องการดูทรานสคริปต์ของนิสิต ?`"
          hide-options
        />
      </div>
    </section>
  </q-page>
</template>

<script setup lang="ts">
/*
    imports
*/

import { useMeta, useQuasar } from 'quasar';
import CustomCard from 'src/components/common/CustomCard.vue';
import { useAuthStore } from 'src/stores/auth';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
/*
    states
*/

const user = useAuthStore();
const { t } = useI18n();
const $q = useQuasar();
const router = useRouter();
type HomeCard = {
  headText: string;
  subText: string;
  goTo: string;
};
// for dev
// const role = computed(() => 'administer');
const homeCards = ref<HomeCard[]>([
  {
    headText: t('curriculums'),
    subText: 'ต้องการสร้างหลักสูตรใหม่ ? / จัดการแก้ไขหลักสูตร ?',
    goTo: `curriculums/management`,
  },
  {
    headText: t('instructors'),
    subText: 'ต้องการดูรายการผู้สอน ? / สร้างผู้สอนใหม่ ?',
    goTo: `instructors/management`,
  },
]);
/*
    methods
*/

const clickOnTranscript = () => {
  $q.dialog({
    title: 'ทรานสคริปต์',
    message: 'กรุณากรอกรหัสนิสิต',
    prompt: { model: '', type: 'text' },
    cancel: {
      label: 'ยกเลิก',
      color: 'grey',
      flat: true,
    },
    persistent: true,
    ok: {
      label: 'ตกลง',
      color: 'primary',
      flat: true,
    },
  }).onOk((data) => {
    router
      .push(`/transcript/${data}`)
      .then()
      .catch((e) => {
        console.error(e);
      });
  });
};

defineOptions({
  name: 'DashboardPage',
});

useMeta({
  title: t('menuLinks.Dashboard'),
});
</script>
