<template>
  <DialogForm
    v-model="store.dialogState"
    :title="store.form.id ? t('form.Edit PLO') : t('form.New PLO')"
    @save="store.handleSave"
    :json="store.form"
  >
    <div class="row q-gutter-y-md">
      <q-input
        v-model="store.form.name"
        outlined
        dense
        class="col-12"
        :label="t('name')"
        :rules="[requireField]"
      />

      <q-input
        dense
        outlined
        class="col-12"
        v-model="store.form.type"
        :label="t('type')"
        :rules="[requireField]"
        counter
        maxlength="300"
      />
      <q-input
        dense
        type="textarea"
        outlined
        class="col-12"
        v-model="store.form.thaiDescription"
        :label="t('description')"
        :rules="[requireField]"
        counter
        maxlength="300"
      />

      <q-input
        dense
        type="textarea"
        outlined
        class="col-12"
        v-model="store.form.engDescription"
        :label="t('englishDescription')"
        :rules="[requireField]"
        counter
        maxlength="300"
      />
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import DialogForm from 'src/components/form/DialogForm.vue';
import { usePloStore } from 'src/stores/plo';
import { requireField } from 'src/utils/field-rules';
import { useI18n } from 'vue-i18n';

const store = usePloStore();
const { t } = useI18n();
</script>

<style scoped></style>
