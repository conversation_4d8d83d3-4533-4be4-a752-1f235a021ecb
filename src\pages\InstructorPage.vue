<template>
  <q-page padding>
    <PageHeader
      v-model:search-text="filterStore.filters['instructors'].nameCodeMail"
      @open-dialog="handleCreate"
      :label-search="t('search') + t('instructor')"
      @enter-search="store.fetchAll"
    />
    <q-table
      flat
      bordered
      :pagination="store.pagination"
      class="q-animate--fade page-table q-mt-lg"
      separator="cell"
      :rows="store.getInstructors"
      row-key="id"
      :loading="global.getLoadingState"
      :columns="instructorColumns"
      :filter="filterStore.search"
      @update:pagination="store.fetchAll"
      @request="(r) => store.fetchAll(r.pagination)"
    >
      <template #body-cell-number="props">
        <q-td>
          {{ props.rowIndex + 1 }}
        </q-td>
      </template>
      <template #body-cell-actions="props">
        <ActionsCell
          @handle-edit="handleEdit(props.row)"
          @handle-delete="store.delete(props.row.id)"
        ></ActionsCell>
      </template>
    </q-table>
  </q-page>
</template>

<script lang="ts" setup>
import { useMeta, useQuasar } from 'quasar';
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { useInstructorStore } from 'src/stores/instructor';
import { useGlobalStore } from 'src/stores/global';
import PageHeader from 'src/components/common/PageHeader.vue';
import { useI18n } from 'vue-i18n';
import InstructorDialog from 'src/features/instructor/dialog/InstructorForm.vue';
import ActionsCell from 'src/components/table/ActionsCell.vue';
import type { Instructor } from 'src/types/education';
import useFilterStore from 'src/stores/filter-search';
import { instructorColumns } from 'src/data/column_table';

const store = useInstructorStore();
const filterStore = useFilterStore();
const global = useGlobalStore();
const { t } = useI18n();
const route = useRoute();
const $q = useQuasar();
const title = computed(() => route.matched[1]?.name as string);

const handleCreate = () => {
  store.resetForm();
  store.titleForm = 'form.New Instructor';
  $q.dialog({
    component: InstructorDialog,
  });
};

const handleEdit = (item: Instructor) => {
  store.resetForm();
  store.titleForm = 'form.Edit Instructor';
  store.form = {
    ...item,
    branchId: item.branch?.id || -1,
  };
  $q.dialog({
    component: InstructorDialog,
  });
};

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>
