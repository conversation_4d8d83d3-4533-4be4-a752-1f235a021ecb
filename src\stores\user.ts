import { defineStore } from 'pinia';
import { Dialog, Notify, type QTableProps } from 'quasar';
import { UserService } from 'src/services/user';
import type { User } from 'src/types/user';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import { i18n } from 'src/boot/i18n';
import { updateTable } from 'src/utils/table';
import { nextTick } from 'vue';
import useFilterStore from './filter-search';

const { t } = i18n.global;

type TitleForm = 'form.New User' | 'form.Edit User' | 'form.Delete User';

export const useUserStore = defineStore('user', {
  state: () => ({
    users: [] as User[],
    form: {} as Partial<User>,
    dialogState: false,
    titleForm: 'form.New User' as TitleForm,
    pagination: { ...defaultPagination },
    editMode: false,
  }),

  getters: {},
  actions: {
    async fetchAll(pag?: QTableProps['pagination']) {
      const filterStore = useFilterStore();
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
      if (emailRegex.test(filterStore.getSearch)) {
        filterStore.getQuery.email = filterStore.getSearch
      } else {
        delete filterStore.getQuery.email
      }
      const { data, total } = await UserService.getAll(
        convertToQuery(pag || this.pagination, filterStore.getQuery),
      );
      this.users = data;
      this.pagination = { ...this.pagination, rowsNumber: total };
    },
    toggleDialog({ form, title }: { form?: Partial<User>; title?: TitleForm }) {
      this.titleForm = title || 'form.New User';
      if (form) {
        this.form = form;
      } else {
        this.resetForm();
      }
      this.dialogState = !this.dialogState;
    },
    async handleSave() {
      if (this.titleForm === 'form.Edit User') {
        await this.updateOne();
      } else {
        await UserService.createOne(this.form as User);
      }
      await UserService.getAll();
      await this.fetchAll();
      this.resetForm();
      this.dialogState = false;
    },

    handleDelete(id: number) {
      Dialog.create({
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this Subject?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        void nextTick(async () => {
          await this.deleteOne(id);
        });
      });
    },

    async updateOne() {
      try {
        const data = await UserService.updateOne(this.form);
        if (data) {
          Notify.create({
            type: 'ok',
            message: t('notify.updateSuccess'),
          });
          this.users = updateTable<User>(data, this.users, 'update');
        }
      } catch (error) {
        console.error('Error updating subject:', error);
      }
    },

    async deleteOne(id: number) {
      const data = await UserService.removeOne(id);
      if (data) {
        Notify.create({
          type: 'ok',
          message: t('notify.deleteSuccess'),
        });
        this.users = updateTable<User>(data, this.users, 'delete');
      }
    },

    resetForm() {
      this.form = {} as Partial<User>;
    },
  },
});
