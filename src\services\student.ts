import { api } from 'boot/axios';
import type {
  DataResponse,
  QueryParams,
  SheetStudentPayload,
} from 'src/types/api';
import type { Student } from 'src/types/education';
export class StudentService {
  static path = 'students';

  static async postImportedStudents(items: SheetStudentPayload[]) {
    const res = await api.post(`${this.path}/import`, items);
    return res.data;
  }

  static async getAll(p?: Partial<QueryParams>) {
    const res = await api.get<DataResponse<Student>>(this.path, { params: p });
    return res.data;
  }

  static async getOne(code: string) {
    const res = await api.get(`${this.path}/${code}`);

    return res.data;
  }

  static async getSkillTree(id: number) {
    const res = await api.get(`${this.path}/skill-tree/${id}`);
    return res.data;
  }

  static async createOne(obj: Partial<Student>) {
    const res = await api.post(this.path, obj);
    return res.data;
  }

  static async updateOne(obj: Partial<Student>) {
    const dto: Partial<Student> = {
      code: obj.code,
      engName: obj.engName,
      thaiName: obj.thaiName,
      branchId: obj.branchId,
    };
    const res = await api.patch(`${this.path}/${obj.id}`, dto);
    return res.data;
  }

  static async removeOne(id: number) {
    const res = await api.delete(`${this.path}/${id}`);
    return res.data;
  }
}
