import { defineStore } from '#q-app/wrappers';
import { createPinia } from 'pinia';
import { i18n } from 'src/boot/i18n';
import type { I18n } from 'vue-i18n';

/*
 * When adding new properties to stores, you should also
 * extend the `PiniaCustomProperties` interface.
 * @see https://pinia.vuejs.org/core-concepts/plugins.html#typing-new-store-properties
 */
declare module 'pinia' {
  export interface PiniaCustomProperties {
    // add your custom properties here, if any
    i18n: I18n;
  }
}
/*
 * If not building with SSR mode, you can
 * directly export the Store instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Store instance.
 */

export default defineStore((/* { ssrContext } */) => {
  const pinia = createPinia();

  // You can add Pinia plugins here
  // pinia.use(SomePiniaPlugin)
  pinia.use(({ store }) => {
    store.$i18n = i18n;
  });

  return pinia;
});
