<template>
  <q-page>
    <div v-if="!$route.params.id">
      <PageHeader
        v-model:searchText="filterStore.filters['courses'].nameCode"
        @open-dialog="handleOpenDialog"
        :label-add="t('newCourse')"
        :label-search="t('search') + t('course')"
        @enter-search="store.fetchAll(undefined, currStore.getInsertId)"
      />
      <q-scroll-area
        v-if="store.courses.length > 0"
        class="q-mt-md"
        style="height: calc(100vh - 200px)"
      >
        <CourseGroup
          v-for="(records, index) in store.getGroupCourse"
          :group="records.group"
          :courses="records.courses"
          :curriculum-code="(route.params.code as string) || ''"
          :key="index"
        />
      </q-scroll-area>
      <section v-else class="q-mt-lg col-auto">
        <div class="text-grey-6 text-center text-h5">{{ t('noData') }}</div>
      </section>
    </div>
    <!-- Course ID page -->
  <router-view />
  </q-page>
</template>

<script lang="ts" setup>
/*
    imports
*/
import { useMeta, useQuasar } from 'quasar';
import { useCourseStore } from 'src/stores/course';
import { computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import NewCourseForm from 'src/features/curriculum/tabs/course/dialog/NewCourseForm.vue';
import PageHeader from 'src/components/common/PageHeader.vue';
import CourseGroup from 'src/features/curriculum/tabs/course/section/CourseGroup.vue';
import useFilterStore from 'src/stores/filter-search';
import { useCurriculumStore } from 'src/stores/curriculum';

const $q = useQuasar();
const { t } = useI18n();
const route = useRoute();
const store = useCourseStore();
const title = computed(() => route.matched[1]?.name as string);
const currStore = useCurriculumStore();
const filterStore = useFilterStore();

const handleOpenDialog = () => {
  $q.dialog({
    component: NewCourseForm,
  });
};

onMounted(async () => {
  filterStore.setPage('courses');
  await store.fetchAll(undefined, currStore.getInsertId);
});

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>
