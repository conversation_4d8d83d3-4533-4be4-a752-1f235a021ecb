<template>
  <q-chip
    v-if="domain"
    outline
    dense
    :color="calSkillType(domain) === 'Hard Skill' ? 'purple' : 'blue'"
  >
    <span class="text-caption">{{ calSkillType(domain) }}</span></q-chip
  >
</template>

<script setup lang="ts">
import type { Skill } from 'src/types/education';
import { calSkillType } from 'src/utils/skill';

defineProps<{
  domain: Skill['domain'];
}>();
</script>

<style scoped></style>
