{"no.": "No.", "actions": "Actions", "home": "Home", "dashboard": "Dashboard", "skills": "Skills", "branch": "Branch", "courses": "Courses", "course": "Course", "courseId": "Course ID", "subjects": "Subjects", "curriculums": "Curriculums", "allCurriculums": "Curriculums", "users": "Users", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "students": "Students", "about": "About", "hello": "<PERSON>dy", "search": "Search", "add": "Add", "edit": "Edit", "remove": "Remove", "delete": "Delete", "save": "Save", "cancel": "Cancel", "filter": "Filter", "faculty": "Faculty", "noBranch": "No Branch", "noFaculty": "No Faculty", "Right click to open menu of each row": "Right click to open menu of each row", "quit": "Quit", "login": "<PERSON><PERSON>", "logout": "Logout", "account": "Account", "insertSubSkill": "Insert Sub-Skill", "settings": "Settings", "darkMode": "Dark Mode", "lightMode": "Light Mode", "changeLanguage": "Change Language", "language": "Language", "export": "Export", "import": "Import", "importStudents": "Import Students", "selectSheet": "Select Sheet", "expectedLevel": "Expected Level", "expectedSkillsLevel": "Expected Skills Level", "evaluation": "Evaluation", "pass": "Pass", "fail": "Fail", "notEvaluated": "Not Evaluated", "gainedLevel": "Gained Level", "passed": "Passed", "notPassed": "Not Passed", "result": "Result", "noCurriculum": "No Curriculum", "noSubject": "No Subject", "courseEnrollment": "Course Enrollment", "faculties & branches": "Faculties & Branches", "newBranch": "New Branch", "newSubject": "New Subject", "name": "Name", "engName": "English Name", "abbrev": "Abbreviation", "description": "Description", "thaiDescription": "Description (TH)", "engDescription": "Description (ENG)", "englishDescription": "Description (ENG)", "curriculum": "Curriculum", "credit": "Credit", "degree": "Degree", "engDegree": "English Degree", "period": "Period", "minimumGrade": "Minimum Grade", "coordinator": "Coordinator", "coordinators": "Coordinators", "coordinatorsManagement": "Coordinators Management", "instructors": "Instructors", "noInstructor": "No Instructor", "noCoordinator": "No Coordinator", "newCoordinator": "New Coordinator", "newInstructor": "New Instructor", "curriculumManagement": "Curriculums Management", "subjectManagement": "Subjects Management", "main": "Main", "next": "Next", "back": "Back", "notFound": "Not Found", "notFoundPage": "Not Found <PERSON>, If you think this is a mistake, please contact the administrator", "error": "Error", "goHome": "Go Home", "showOnlyWithSubSkill": "Show only skills with sub-skills", "confirm": "Confirm", "clear": "Clear", "summary": "Summary", "noData": "No Data", "branches": "Branches", "id": "ID", "code": "Code", "curriculumCode": "Curriculum Code", "subjectCode": "Subject Code", "selectSubject": "Select Subject", "details": "Details", "specialists": "Specialists", "addCoordinators": "Add Coordinators", "newCurriculum": "New Curriculum", "createCurriculum": "Create a Curriculum", "deleteCurriculum": "Delete Curriculum", "addCoordinator": "Add a Coordinator", "addBranch": "Add a Branch", "createInstructor": "Create a In<PERSON>ructor", "createBranch": "Create a Branch", "createFaculty": "Create a Faculty", "Coordinators of Curriculum": "Coordinators of Curriculum", "assign": "Assign", "PLOs": "PLOs", "ploManagement": "PLO Management", "cloManagement": "CLO Management", "type": "Type", "email": "Email", "position": "position", "newCourse": "New Course", "semester": "<PERSON><PERSON><PERSON>", "firstSemester": "First Semester", "secondSemester": "Second Semester", "summerSemester": "Summer Semester", "academicYear": "Academic Year", "availableCourses": "Available Courses", "archivedCourses": "Archived Courses", "downloadExampleTemplate": "Download Example Template", "clickOrDragAndDropFileHere": "Click or Drag and Drop File Here", "upload": "Upload", "newFaculty": "New Faculty", "role": "Role", "tel.": "Tel.", "password": "Password", "forgetPassword": "Forget Password", "updateScore": "Update Score", "transcript": "Transcript", "Please select a curriculum first": "Please select a curriculum first", "quickStart": "Quick Start", "officeRoom": "Office Room", "bio": "Biography", "landingPage": "<PERSON>", "totalSkills": "Total Skills", "assignCoordinator": "Assign Coordinator", "invalidEmailOrPassword": "Invalid email or password", "nameOfSkill": "Name of skill", "selectFaculty": "Select Faculty", "selectBranch": "Select Branch", "selectCodeYears": "Select Code Years", "searchBy": "Search by", "validation": {"required": "This field is required", "email": "Invalid email format", "tel": {"pattern": "Invalid phone number format"}, "existCode": "This code already exists", "availableCode": "Available code"}, "menuLinks": {"Dashboard": "Dashboard", "skills": "Skill Management", "subjects": "Subject Management", "curriculums": "Curriculums Management", "courses": "Course Management", "users": "User Management", "students": "Student Management", "instructors": "Instructor Management", "faculties": "Faculty & Branch Management"}, "total": "Total", "filters": {"yearCode": "Year Code"}, "searchByIndex": {"nameCode": "Name / Code", "skill": "Skill"}, "dialog": {"confirm": "Confirm", "deleteConfirm": "Are you sure you want to delete this", "deleteConfirmTitle": "Confirm Deletion", "cancel": "Cancel"}, "notify": {"updateSuccess": "Updated successfully", "createSuccess": "Created successfully", "deleteSuccess": "Deleted successfully"}, "skillTab": {"new-skill": "New Skill", "insert-sub-skill": "Insert Sub-Skill", "edit": "<PERSON>", "delete": "Delete Skill"}, "form": {"index": "Form", "create": "Create", "edit": "Edit", "delete": "Delete", "view": "View", "submit": "Submit", "reset": "Reset", "New Skill": "New Skill", "Edit Skill": "<PERSON>", "New Subject": "New Subject", "Edit Subject": "Edit Subject", "New Course": "New Course", "Edit Course": "Edit Course", "New Curriculum": "New Curriculum", "Edit Curriculum": "Edit <PERSON>", "New Instructor": "New Instructor", "Edit Instructor": "Edit Instructor", "New Coordinator": "New Coordinator", "Edit Coordinator": "Edit Coordinator", "New Student": "New Student", "Edit Student": "Edit Student", "Add Student": "Add Student", "New CLO": "New CLO", "Edit CLO": "Edit CLO", "Edit PLO": "Edit PLO", "New PLO": "New PLO", "New User": "New User", "Edit User": "Edit User", "New Faculty": "New Faculty", "Edit Faculty": "Edit Faculty", "New Branch": "New Branch", "Edit Branch": "Edit Branch"}}