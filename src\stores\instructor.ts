import { defineStore } from 'pinia';
import { Notify, Dialog, type QTableProps } from 'quasar';
import { InstructorService } from 'src/services/instructor';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import { useCurriculumStore } from './curriculum';
import { nextTick } from 'vue';
import type { QueryParams } from 'src/types/api';
import type { Instructor, Coordinator } from 'src/types/education';
import useFilterStore from './filter-search';

type TitleForm = 'form.New Instructor' | 'form.Edit Instructor';

export const useInstructorStore = defineStore('instructor', {
  state: () => ({
    dialogState: false,
    search: '',
    form: {} as Instructor,
    listCoordinator: [] as Coordinator[],
    listInstructor: [] as Instructor[],
    availableItem: [] as Instructor[],
    titleForm: '' as TitleForm,
    foundCode: false,
    pagination: { ...defaultPagination },
    filterModel: {} as Partial<QueryParams>,
    codeLabel: '',
    isFoundCode: false,
    curr: useCurriculumStore(),
  }),

  getters: {
    getInstructors: (s) => s.listInstructor,
    getCoordinators: (s) => s.listCoordinator,
    getTitleForm: (s) => s.titleForm,
    getCtaText: (s) => {
      if (s.titleForm === 'form.New Instructor') {
        return 'createInstructor';
      } else {
        return 'save';
      }
    },
    getAvailableInstructors: (s) => s.availableItem,
  },

  actions: {
    async fetchAll(pag?: QTableProps['pagination']) {
      const filterStore = useFilterStore();
      this.filterModel = filterStore.filters.instructors;
      this.pagination = pag || this.pagination;
      const { data, total } = await InstructorService.getAll(
        convertToQuery(this.pagination, this.filterModel),
      );
      this.listInstructor = JSON.parse(JSON.stringify(data));
      this.pagination.rowsNumber = total || 0;
    },
    async fetchAllInCurr(
      pag?: QTableProps['pagination'],
      curriculumId?: number | string,
    ) {
      const currId = this.curr.getInsertId || curriculumId;
      this.filterModel.curriculumId = currId;
      this.pagination = pag || this.pagination;
      const { data, total } = await InstructorService.getAll(
        convertToQuery(this.pagination, this.filterModel),
      );
      this.listCoordinator = JSON.parse(JSON.stringify(data));
      this.pagination.rowsNumber = total || 0;
    },
    async fetchAvailableInstructors(pag: QTableProps['pagination']) {
      const filter: Partial<QueryParams> = {
        branchId: String(this.curr.getBranch?.id),
      };
      const { data } = await InstructorService.getAll(
        convertToQuery(pag, filter),
      );
      this.availableItem = JSON.parse(JSON.stringify(data));
    },
    async assignInstructor(params: {
      curriculumId: number;
      instructorId: number;
    }) {
      const ok = await InstructorService.assignInstructor(params);
      if (ok) {
        Notify.create({
          type: 'ok',
          message: 'Updated successfully',
        });
        await nextTick();
        await this.fetchAllInCurr();
        this.toggleDialog();
      }
    },

    removeAssignedInstructor(params: {
      curriculumId: number;
      instructorId: number;
    }) {
      Dialog.create({
        title: 'Confirm Deletion',
        message: 'Are you sure you want to revoke this instructor?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        InstructorService.removeAssignedInstructor(params)
          .then(async (ok) => {
            if (ok) {
              Notify.create({
                type: 'ok',
                message: `Deleted successfully`,
              });
              await nextTick();
              await this.fetchAllInCurr();
            }
          })
          .catch((err) => console.log(err));
      });
    },
    resetForm() {
      this.form = {} as Instructor;
    },

    async createOne() {
      const ok = await InstructorService.createOne(this.form);
      if (ok) {
        Notify.create({
          type: 'ok',
          message: 'Created successfully',
        });
      }
      await this.fetchAll();
    },
    async updateOne() {
      const ok = await InstructorService.updateOne(this.form as Instructor);
      if (ok) {
        Notify.create({
          type: 'ok',
          message: 'Updated successfully',
        });
      }
      await this.fetchAll();
    },

    async delete(id: number) {
      const ok = await InstructorService.removeOne(id);
      if (ok) {
        Notify.create({
          type: 'ok',
          message: `Deleted successfully`,
        });
      }
      await this.fetchAll();
    },

    handleDelete(id: number) {
      Dialog.create({
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this instructor?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        void nextTick(async () => {
          await this.delete(id);
        });
      });
    },

    async handleSave() {
      if (this.titleForm === 'form.Edit Instructor') {
        await this.updateOne();
      } else {
        await this.createOne();
      }
      this.dialogState = false;
      this.resetForm();
      await this.fetchAll();
    },

    handleEdit(form: Instructor) {
      this.form = { ...form };
      this.titleForm = 'form.Edit Instructor';
      this.toggleDialog();
    },

    handleCreate() {
      this.resetForm();
      this.titleForm = 'form.New Instructor';
      this.toggleDialog();
    },

    toggleDialog() {
      this.dialogState = !this.dialogState;
    },
  },
});
