import type { User } from './user';

export interface DataResponse<T> {
  data: Array<T>;
  total: number;
}

export type QueryParams = {
  page: number;
  limit: number;
  sort: string;
  orderBy: 'asc' | 'desc';
  curriculumId: string | number;
  email: string;
  // * search
  nameCode: string;
  nameCodeMail: string
  skillName: string;
  thaiName: string;
  engName: string;
  // * filter
  branchId: string | number;
  facultyId: string | number;
  codeYears: Array<string | number>;
  years: number[];
  semesters: number[];
};

export interface AuthPayload {
  messages: string;
  accessToken: string;
  refreshToken: string;
  statusCode: number;
  user: User;
}

export interface SheetStudentPayload {
  code: string;
  thaiName: string;
  engName: string;
  branchId: number;
  enrollmentDate: Date;
  curriculumId?: number;
}
