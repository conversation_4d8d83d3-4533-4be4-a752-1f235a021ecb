<template>
  <q-page class="q-gutter-y-md q-mt-md">
    <PageTitle :title="t('ploManagement')" />
    <PageHeader @open-dialog="onClickAddBtn" hide-filter hide-search />
    <q-table
      flat
      bordered
      :loading="global.getLoadingState"
      class="q-mt-md q-animate--fade"
      :rows="store.getListPLO"
      :columns="ploColumns"
      row-key="name"
      wrap-cells
      separator="cell"
      @update:pagination="store.fetchAllInCurr"
    >
      <template #body-cell-number="props">
        <q-td>
          {{ props.rowIndex + 1 }}
        </q-td>
      </template>
      <template #body-cell-actions="props">
        <ActionsCell
          @handle-delete="store.handleDelete(props.row)"
          @handle-edit="onClickEdit(props.row)"
        />
      </template>
    </q-table>
  </q-page>
</template>

<script lang="ts" setup>
import { useQuasar } from 'quasar';
import { useGlobalStore } from 'src/stores/global';
import { usePloStore } from 'src/stores/plo';
import PloDialog from 'src/features/curriculum/dialog/PloForm.vue';
import { useI18n } from 'vue-i18n';
import ActionsCell from 'src/components/table/ActionsCell.vue';
import PageHeader from 'src/components/common/PageHeader.vue';
import PageTitle from 'src/components/common/PageTitle.vue';
import type { PLO } from 'src/types/education';
import { ploColumns } from 'src/data/column_table';

const $q = useQuasar();
const { t } = useI18n();
const global = useGlobalStore();
const store = usePloStore();

const onClickAddBtn = () => {
  store.resetForm();
  store.titleForm = 'form.New PLO';
  $q.dialog({
    component: PloDialog,
  });
};

const onClickEdit = (item: PLO) => {
  store.titleForm = 'form.Edit PLO';
  store.form = item;
  $q.dialog({
    component: PloDialog,
  });
};
</script>
