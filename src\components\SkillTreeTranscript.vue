<template>
  <div v-if="skill.subskills.length > 0" class="q-pl-md q-mt-xs q-mb-md">
    <div v-for="(sub, index) in skill.subskills" :key="index" class="q-mb-xs">
      <div>
        <strong>{{ sub.name }}</strong>
        <span> ({{ sub.domain }})</span>
        {{ sub.subskills.length > 0 ? '' : '- Level: ' + sub.gained }}
      </div>
      <!-- recursive เรียก sub-subskills -->
      <SkillTreeTranscript :skill="sub" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { type SkillItem } from 'src/pages/public/TranscriptPage.vue';

defineProps<{ skill: SkillItem }>();
</script>
