<template>
  <DialogForm
    :title="store.formFaculty.id ? t('form.Edit Faculty') : t('form.New Faculty')"
    v-model="store.dialogState"
    @save="store.handleSave"
    cta-text="createFaculty"
    :json="store.getJsonForm"
  >
    <div class="q-gutter-y-md">
      <q-input
        outlined
        dense
        :label="t('name')"
        v-model="store.formFaculty.thaiName"
        :rules="[requireField]"
      />
      <q-input
        outlined
        dense
        :label="t('engName')"
        v-model="store.formFaculty.engName"
        :rules="[requireField]"
      />
      <q-input
        outlined
        dense
        label="Abbreviation"
        hint="Optional"
        v-model="store.formFaculty.abbrev"
      />
      <q-input
        outlined
        dense
        :label="t('description')"
        hint="Optional"
        type="textarea"
        counter
        maxlength="1000"
        v-model="store.formFaculty.thaiDescription"
      />
      <q-input
        outlined
        dense
        :label="t('englishDescription')"
        hint="Optional"
        type="textarea"
        counter
        maxlength="1000"
        v-model="store.formFaculty.engDescription"
      />
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import { useFacultyStore } from 'src/stores/faculty-branch';
import { requireField } from 'src/utils/field-rules';
import { useI18n } from 'vue-i18n';
import DialogForm from 'src/components/form/DialogForm.vue';

const store = useFacultyStore();
const { t } = useI18n();
</script>

<style scoped></style>
