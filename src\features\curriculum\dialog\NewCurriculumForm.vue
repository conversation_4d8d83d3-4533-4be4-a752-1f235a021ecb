<template>
  <DialogForm
    v-model="store.dialogState"
    :title="t('form.New Curriculum')"
    :cta-text="'createCurriculum'"
    @save="store.handleCreateOne()"
    :json="store.form"
    width="50%"
  >
    <div class="row q-gutter-y-md">
      <FieldChecker
        :label="t('curriculumCode')"
        v-model="store.form.code"
        :func-update="store.checkUpdateCode"
        :found-hint="store.getCurriculumCodeLabel"
        :is-found="store.foundExistCurriculum"
        mask="##############"
        :rules="[
          (val: string) =>
            val.length == 14 || 'Field not correct format (14 digits)',
        ]"
      />
      <div class="row col-12">
        <q-input
          dense
          outlined
          v-model="store.form.thaiName"
          :label="t('name') + ' *'"
          class="col"
        />
        <q-input
          dense
          outlined
          v-model="store.form.engName"
          :label="t('engName') + ' *'"
          :rules="[requireField]"
          class="col q-ml-md"
        />
      </div>
      <div class="row col-12">
        <q-select
          dense
          outlined
          :options="OptionEducationLevelTH"
          v-model="store.form.thaiDegree"
          :label="t('degree') + ' *'"
          :rules="[requireField]"
          class="col"
        />
        <q-select
          dense
          outlined
          v-model="store.form.engDegree"
          :options="OptionEducationLevelEN"
          :label="t('engDegree') + ' *'"
          :rules="[requireField]"
          class="col q-ml-md"
        />
      </div>
      <q-input
        dense
        outlined
        v-model.number="store.form.period"
        :label="t('period') + ' *'"
        mask="#"
        :rules="[requireField]"
        class="col-2 q-mr-md"
      >
      </q-input>
      <q-input
        dense
        outlined
        mask="#.##"
        v-model="store.form.minimumGrade"
        :label="t('minimumGrade') + ' *'"
        :rules="[requireField, ruleGradeFormat]"
        class="col-2 q-mr-md"
      />
      <div class="col">
        <FieldBranchOptions
          v-model="store.form.branch"
          v-model:branch-id="store.form.branchId"
        />
      </div>
      <q-input
        dense
        type="textarea"
        outlined
        v-model="store.form.thaiDescription"
        :label="t('description') + ' *'"
        :rules="[requireField]"
        class="col-12"
        counter
        maxlength="1000"
      />
      <q-input
        dense
        type="textarea"
        outlined
        v-model="store.form.engDescription"
        :label="t('englishDescription') + ' *'"
        :rules="[requireField]"
        class="col-12"
        counter
        maxlength="1000"
      >
      </q-input>
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import DialogForm from 'src/components/form/DialogForm.vue';
import { useCurriculumStore } from 'src/stores/curriculum';
import { requireField, ruleGradeFormat } from 'src/utils/field-rules';
import { useI18n } from 'vue-i18n';
import FieldChecker from 'src/components/form/FieldChecker.vue';
import FieldBranchOptions from 'src/components/form/FieldBranchOptions.vue';
import {
  OptionEducationLevelEN,
  OptionEducationLevelTH,
} from 'src/data/education_level';
import { onMounted, watch } from 'vue';

const store = useCurriculumStore();
const { t } = useI18n();

onMounted(() => {
  store.resetForm();
});

watch(
  () => [store.form.thaiDegree, store.form.engDegree],
  ([newThaiDegree, newEngDegree], [oldThaiDegree, oldEngDegree]) => {
    // Avoid infinite loops by checking if the value has actually changed
    if (newThaiDegree !== oldThaiDegree) {
      const indexTH = OptionEducationLevelTH.indexOf(newThaiDegree);
      if (indexTH !== -1) {
        store.form.engDegree = OptionEducationLevelEN[indexTH]!;
      }
    } else if (newEngDegree !== oldEngDegree) {
      const indexEN = OptionEducationLevelEN.indexOf(newEngDegree);
      if (indexEN !== -1) {
        store.form.thaiDegree = OptionEducationLevelTH[indexEN]!;
      }
    }
  },
  { deep: true },
);
</script>

<style scoped></style>
