<template>
  <q-page padding>
    <MainHeader
      v-model:search-text="filterStore.search"
      v-model:filter-model="store.filterModel"
      @open-dialog="handleNewBtn"
      @enter-search="store.fetchAll"
      :label-add="t('newCurriculum')"
      :label-search="t('search') + t('curriculum')"
    />
    <q-table
      flat
      bordered
      :loading="global.getLoadingState"
      class="q-mt-md q-animate--fade page-table"
      :rows="store.getCurriculums"
      :pagination="store.pagination"
      :columns="columns"
      row-key="code"
      wrap-cells
      separator="cell"
    >
      <template #body="props">
        <q-tr
          :props="props"
          class="cursor-pointer"
          @click="handleClickRow(props.row)"
        >
          <q-td class="cell-head-number" key="number" :props="props"> {{ props.rowIndex + 1 }}</q-td>
          <q-td key="code" :props="props">{{ props.row.code }}</q-td>
          <q-td key="name" :props="props">
            <q-item-label class="q-mb-xs">
              <q-item-label class="text-body1">
                {{ props.row.thaiName }}
              </q-item-label>
              <q-item-label caption>
                {{ props.row.engName }}
              </q-item-label>
            </q-item-label>
          </q-td>
          <q-td key="degree" :props="props">{{ props.row.thaiDegree }}</q-td>
          <q-td key="branch" :props="props">{{
            props.row.branch?.thaiName
          }}</q-td>
        </q-tr>
      </template>
    </q-table>
  </q-page>
</template>

<script lang="ts" setup>
import type { QTableColumn } from 'quasar';
import { useMeta, useQuasar } from 'quasar';
import MainHeader from 'src/components/common/PageHeader.vue';
import { useCurriculumStore } from 'src/stores/curriculum';
import { computed, nextTick, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useGlobalStore } from 'src/stores/global';
import NewCurriculumForm from 'src/features/curriculum/dialog/NewCurriculumForm.vue';
import { useI18n } from 'vue-i18n';
import useFilterStore from 'src/stores/filter-search';

import type { Curriculum, Branch } from 'src/types/education';

const $q = useQuasar();
const { t } = useI18n();
const global = useGlobalStore();
const route = useRoute();
const router = useRouter();
const title = computed(() => route.matched[1]?.name as string);
const store = useCurriculumStore();
const filterStore = useFilterStore();
const columns = computed(
  () =>
    <QTableColumn[]>[
      { name: 'number', label: t('no.'), field: 'no', align: 'left' },
      {
        name: 'code',
        label: t('curriculumCode'),
        field: 'code',
        align: 'left',
      },
      { name: 'name', label: t('name'), field: 'thaiName', align: 'left' },
      {
        name: 'degree',
        label: t('degree'),
        field: 'thaiDegree',
        align: 'left',
        style: 'min-width: 130px',
      },
      {
        name: 'branch',
        label: t('branch'),
        field: (b: Branch) => b.thaiName,
        align: 'left',
      },
    ],
);

onMounted(async () => {
  await nextTick(async () => {
    await store.fetchAll();
  });
});

const handleNewBtn = () => {
  $q.dialog({ component: NewCurriculumForm });
};

const handleClickRow = async (row: Curriculum) => {
  await router.push(`/curriculums/${row.code}/summary`);
};

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>
