import { useCurriculumStore } from 'src/stores/curriculum';
import type { RouteRecordRaw } from 'vue-router';

export const curriculumRoutes: RouteRecordRaw[] = [
  {
    path: '/curriculums',
    name: 'Curriculum',
    component: () => import('src/layouts/CurriculumLayout.vue'),
    beforeEnter: async (to, from, next) => {
      const store = useCurriculumStore();
      const code = to.params.code as string;

      try {
        if (!code) {
          console.warn('No curriculum code found in route params.');
          return;
        }
        await store.fetchOneByCode(code); // Fetch curriculum data
        next(); // Proceed to the route
      } catch (error) {
        console.error('Failed to fetch curriculum:', error);
        next('/error'); // Redirect to an error page if needed
      }
    },
    children: [
      {
        path: ':code',
        name: 'Curriculum Code',
        component: () =>
          import('src/features/curriculum/tabs/CurriculumTab.vue'),
      },
      {
        path: ':code/coordinators',
        name: 'Coordinators of Curriculum',
        component: () =>
          import('src/features/curriculum/tabs/CoordinatorsTab.vue'),
      },
      {
        path: ':code/plos',
        name: 'PLOs',
        component: () => import('src/features/curriculum/tabs/PloTab.vue'),
      },
      {
        path: ':code/skills',
        name: 'Skills of Curriculum',
        component: () => import('src/features/curriculum/tabs/SkillTab.vue'),
      },
      {
        path: ':code/subjects/:subjectId/clos',
        name: 'ClOs',
        component: () => import('src/features/curriculum/dialog/CloDialog.vue'),
      },
      {
        path: ':code/subjects',
        name: 'Subjects of Curriculum',
        component: () => import('src/features/curriculum/tabs/SubjectTab.vue'),
      },
      {
        path: ':code/students',
        name: 'Students of Curriculum',
        component: () => import('src/features/curriculum/tabs/StudentTab.vue'),
      },
      {
        path: ':code/courses',
        name: 'courses',
        component: () =>
          import('src/features/curriculum/tabs/course/CoursePage.vue'),
        children: [
          {
            path: ':id',
            name: 'course-details',
            component: () =>
              import('src/features/curriculum/tabs/course/CourseDetails.vue'),
          },
        ],
      },
      {
        path: ':code/summary',
        name: 'Summary of Curriculum',
        component: () => import('src/features/curriculum/tabs/SummaryTab.vue'),
        children: [
          {
            path: '',
            name: 'SummaryChartPage',
            component: () => import('src/features/curriculum/summary/SummarySkillChart.vue')
          },
          {
            path: ':skillId/students',
            name: 'SummaryStudentList',
            component: () => import('src/features/curriculum/summary/SummaryStudentList.vue')
          }
        ]
      },
      {
        path: ':code/summary2',
        name: 'Summary2 of Curriculum',
        component: () => import('src/features/curriculum/tabs/SummaryTab2.vue'),
      },

    ],
  },
];
