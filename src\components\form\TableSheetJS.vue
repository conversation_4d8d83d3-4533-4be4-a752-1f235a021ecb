<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import { ref } from 'vue';
import { read, utils, type WorkSheet } from 'xlsx';
import { useI18n } from 'vue-i18n';

type DataSet = { [index: string]: WorkSheet };
type Row = string[];
type RowCB = (row: Row) => string;
type Column = { field: string; label: string; display: RowCB };
type RowCol = { rows: Row[]; cols: Column[] };

const currFileName = ref<string>('');
const currSheet = ref<string>('');
const sheets = ref<string[]>([]);
const workBook = ref<DataSet>({} as DataSet);
const rows = ref<Row[]>([]);
const columns = ref<Column[]>([]);
const loading = ref<boolean>(true);
const paging = ref<boolean>(true);
type SheetRow = Record<string, string | number | boolean | null | undefined>;
const items = ref<SheetRow[]>([]);
// const headerItems = ref<HeaderItem[]>([]);

// export type HeaderItem = Readonly<{
//   title: string;
//   key: object | string;
//   value?: string;
//   sortable?: boolean;
// }>;

function buildEditableHeaderItems(): QTableColumn[] {
  try {
    if (!rows.value[0]) return [];
    return rows.value[0].map((element, index) => {
      if (typeof element === 'string') {
        return {
          name: element,
          label: element,
          field: (row: Row) => row[index] || '',
          align: 'left',
        };
      } else {
        throw new Error(`Invalid element type: ${typeof element}`);
      }
    });
  } catch (e) {
    console.error(e);
    return [];
  }
}
// const exportTypes: string[] = ['xlsx', 'xlsb', 'csv', 'html'];

let cell = 0;

function resetCell() {
  cell = 0;
}

const getRowsCols = (data: DataSet, sheetName: string): RowCol => {
  const sheet = data[sheetName];
  if (!sheet) {
    throw new Error(`Sheet not found: ${sheetName}`);
  }

  const rows = utils.sheet_to_json<Row>(sheet, { header: 1 });
  const range = utils.decode_range(sheet['!ref'] || 'A1');
  if (!range) {
    throw new Error(`Range not found: ${sheetName}`);
  }

  return {
    rows,
    cols: Array.from({ length: range.e.c + 1 }, (_, i) => ({
      field: String(i),
      label: utils.encode_col(i),
      display: makeDisplay(i),
    })),
  };
};

const makeDisplay =
  (col: number): RowCB =>
  (row: Row) =>
    `<span
  style="user-select: none; display: block"
  onblur="endEdit(event)" ondblclick="startEdit(event)"
  position="${Math.floor(cell++ / columns.value.length)}.${col}"
  onkeydown="endEdit(event)">${row?.[col] ?? '&nbsp;'}</span>`;

declare global {
  interface Window {
    startEdit: (ev: MouseEvent) => void;
    endEdit: (ev: FocusEvent | KeyboardEvent) => void;
  }
}

window.startEdit = function (ev: MouseEvent) {
  (ev?.target as HTMLSpanElement).contentEditable = 'true';
};
/**
 * Ends the cell editing and updates the workbook with the new value.
 * @param {FocusEvent | KeyboardEvent} ev - The focus or keyboard event that triggered the end of editing.
 */
window.endEdit = function (ev: FocusEvent | KeyboardEvent): void {
  if (!(ev instanceof KeyboardEvent) || ev.key === 'Enter') {
    const pos = (ev.target as HTMLSpanElement)
      ?.getAttribute('position')
      ?.split('.');
    if (!pos) return;

    const target = ev.target as HTMLSpanElement;
    target.contentEditable = 'true';

    if (!rows.value) {
      return;
    }

    const rowIndex = +(pos[0] || '');
    const colIndex = +(pos[1] || '');

    (rows.value[rowIndex] ??= [])[colIndex] = target.innerText;

    workBook.value[currSheet.value] = utils.json_to_sheet(rows.value, {
      header: columns.value.map((col: Column) => col.field),
      skipHeader: true,
    });
  }
};

function importAB(ab: ArrayBuffer, name: string) {
  loading.value = true;
  const data = read(ab);

  currFileName.value = name;
  currSheet.value = data.SheetNames?.[0] || '';
  sheets.value = data.SheetNames;
  workBook.value = data.Sheets;
  loading.value = false;

  selectSheet(currSheet.value);
}

async function importFile(ev: Event): Promise<void> {
  const file = (ev.target as HTMLInputElement)?.files?.[0];
  if (!file) return;
  importAB(await file.arrayBuffer(), file.name);
}

// function exportFile(type: string): void {
//   const wb = utils.book_new();

//   sheets.value.forEach((sheet) => {
//     utils.book_append_sheet(wb, workBook.value[sheet], sheet);
//   });

//   writeFile(wb, `sheet.${type}`);
// }

function selectSheet(sheet: string): void {
  const { rows: newRows, cols: newCols } = getRowsCols(workBook.value, sheet);

  resetCell();

  currSheet.value = sheet;
  columns.value = newCols;
  rows.value = dropBlankRow(newRows);
  paging.value = newRows.length > 50;

  items.value = utils.sheet_to_json(workBook.value[sheet] ?? []);
}

// onMounted(async () => {
//   const response = await fetch('https://docs.sheetjs.com/pres.numbers');
//   await importAB(await response.arrayBuffer(), 'pres.numbers');
// });

function dropBlankRow(rows: Row[]): Row[] {
  return rows.filter((row) =>
    Object.values(row).some((v) => v !== null && v !== ''),
  );
}

// Local editing functions for Vue template
function updateCell(rowIndex: number, colIndex: number, newValue: string) {
  // Update the rows data (add 1 to rowIndex because we slice(1) the rows in template)
  if (rows.value[rowIndex + 1]) {
    rows.value[rowIndex + 1][colIndex] = newValue;

    // Update workbook
    workBook.value[currSheet.value] = utils.json_to_sheet(rows.value, {
      header: columns.value.map((col: Column) => col.field),
      skipHeader: true,
    });

    // Update items
    items.value = utils.sheet_to_json(workBook.value[currSheet.value] ?? []);
  }
}

const { t } = useI18n();

defineProps<{
  hasTemplate?: true;
  valid: boolean;
  customActions?: true;
}>();

defineExpose({
  items,
});

defineEmits<{
  (e: 'downloadTemplate'): void;
  (e: 'confirm', items: SheetRow[]): void;
}>();

const dropZoneRef = ref<HTMLElement | null>(null);
const isOverDropZone = ref(false);

// Function to handle file drop
async function onDrop(event: DragEvent) {
  event.preventDefault();
  isOverDropZone.value = false; // Reset drop zone state
  const files = event.dataTransfer?.files;
  if (!files || files.length === 0) return;

  const file = files[0];
  if (!file) return;
  importAB(await file.arrayBuffer(), file.name);
}

// Handle the drag events
function onDragEnter(event: DragEvent) {
  event.preventDefault();
  isOverDropZone.value = true;
}

function onDragOver(event: DragEvent) {
  event.preventDefault();
}

function onDragLeave(event: DragEvent) {
  event.preventDefault();
  const relatedTarget = event.relatedTarget as Node | null;
  if (!dropZoneRef.value?.contains(relatedTarget)) {
    isOverDropZone.value = false;
  }
}
</script>

<template>
  <div class="fit">
    <div class="text-center" v-show="!currSheet">
      <div
        class="text-blue-10 cursor-pointer q-my-md"
        @click="$emit('downloadTemplate')"
      >
        {{ t('downloadExampleTemplate') }} <q-icon name="download" />
      </div>
      <div
        ref="dropZoneRef"
        :class="[
          isOverDropZone ? 'bg-blue-1' : '',
          'flex justify-center items-center',
        ]"
        @dragenter="onDragEnter"
        @dragover="onDragOver"
        @dragleave="onDragLeave"
        @drop="onDrop"
        id="dropzone"
      >
        <div class="q-pa-xl">
          <label for="upload" class="cursor-pointer">
            <div class="text-body1">
              <div>{{ t('clickOrDragAndDropFileHere') }}</div>
              <q-icon name="upload_file" class="q-mr-xs q-mt-sm"></q-icon>
            </div>
          </label>
          <input
            id="upload"
            class="hidden"
            type="file"
            @change="importFile"
            accept=".csv,.xlsx,.xlsb"
          />
        </div>
      </div>
    </div>
    <div v-if="currSheet.length > 0">
      <div class="flex flex-center">
        <q-select
          v-model="currSheet"
          :options="sheets"
          outlined
          @update:model-value="selectSheet"
          :label="t('selectSheet')"
          class="full-width"
        />
      </div>
      <q-table
        separator="cell"
        class="q-mt-sm"
        :rows="rows.slice(1)"
        :columns="buildEditableHeaderItems()"
        flat
        bordered
      >
        <template #top>
          <div class="text-h6">{{ currSheet }}</div>
        </template>
        <template #body-cell="props">
          <q-td :props="props">
            <div class="text-pre-wrap">{{ props.value || '' }}</div>
            <q-popup-edit
              :model-value="props.value"
              auto-save
              v-slot="scope"
              @save="(val) => updateCell(props.rowIndex, props.cols.indexOf(props.col), val)"
            >
              <q-input
                v-model="scope.value"
                dense
                autofocus
                counter
                @keydown.enter.prevent="scope.set"
              />
            </q-popup-edit>
          </q-td>
        </template>
      </q-table>
      <div class="q-mt-md flex justify-center" v-if="!customActions">
        <q-btn
          :disable="!valid"
          @click="$emit('confirm', items)"
          class="fit"
          color="primary"
          unelevated
          >{{ t('confirm', items) }}</q-btn
        >
      </div>
      <slot v-else name="actions"></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
#dropzone {
  margin-top: 2rem;
  width: 100%;
  min-height: calc(100vh - 500px);
  height: 100%;
  border: 1px dashed #ccc;
  border-radius: 12px;
}
</style>
