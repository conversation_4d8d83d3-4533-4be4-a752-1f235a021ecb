<template>
  <div class="text-weight-medium q-mb-md">
    {{ t('academicYear') }} {{ group.split('-')[0] }} / {{ t('semester') }}
    {{ group.split('-')[1] }}
  </div>
  <div class="q-gutter-lg q-mb-md row">
    <div v-for="(course, index) in courses" :key="index" class="cursor-pointer">
      <CustomCard
        :head-text="course.subject.thaiName || ''"
        :sub-text="course.subject.engName || ''"
        @handle-view="store.clickViewCourse(curriculumCode!, course.id!)"
      >
        <template #btn-options>
          <q-item clickable>
            <q-item-section @click="store.removeOne(course)">
              <div class="flex items-center justify-around">
                <q-icon size="16px" name="delete" class="q-mr-sm" />
                <div>
                  {{ t('remove') }}
                </div>
              </div>
            </q-item-section>
          </q-item>
        </template>
      </CustomCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useCourseStore } from 'src/stores/course';
import CustomCard from 'src/components/common/CustomCard.vue';
import type { Course } from 'src/types/education';

defineProps<{
  group: string;
  courses: Course[];
  curriculumCode: string;
}>();

const { t } = useI18n();
const store = useCourseStore();
</script>

