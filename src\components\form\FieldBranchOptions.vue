<template>
  <q-select
    outlined
    dense
    v-model="model"
    :options="branches"
    option-label="thaiName"
    :option-value="optionBranchValue"
    map-options
    :label="t('branch')"
    class="col-12"
    options-dense
    :rules="[requireField]"
    :lazy-rules="true"
  />
</template>

<script setup lang="ts">
import { BranchService } from 'src/services/branch';
import type { Branch } from 'src/types/education';
import { requireField } from 'src/utils/field-rules';
import { nextTick, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const model = defineModel<Partial<Branch> | undefined | null>({
  default: null,
});
const modelBranchId = defineModel<number | undefined | null>('branchId', {
  default: null,
});
const branches = ref<Branch[]>([]);

defineProps<{
  returnFullBranch?: true;
}>();

watch(
  () => model.value,
  (val) => {
    if (val) {
      modelBranchId.value = val.id;
    }
  },
);

const optionBranchValue = (branch: Branch) => branch.id;

onMounted(async () => {
  await nextTick();
  branches.value = await BranchService.getOptions();
  if (modelBranchId.value) {
    model.value = branches.value.find(
      (branch) => branch.id === modelBranchId.value,
    );
  }
});
</script>

<style scoped></style>
