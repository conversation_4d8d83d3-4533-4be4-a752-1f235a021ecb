<template>
  <DialogForm
    v-model="store.dialogState"
    :title="store.form.id ? t('form.Edit Subject') : t('form.New Subject')"
    @save="store.handleSave()"
    :json="store.form"
  >
    <div class="q-gutter-y-md">
      <FieldChecker
        :label="t('subjectCode')"
        v-model="store.form.code"
        :func-update="(s) => store.checkSubjectCode(String(s))"
        :found-hint="store.getSubjectCodeLabel"
        :is-found="store.foundExistSubject"
      />
      <q-select
        v-model="store.form.type"
        outlined
        dense
        :label="t('type')"
        :options="OptionSubjectType"
        :rules="[requireField]"
      >
      </q-select>
      <q-input
        v-model="store.form.credit"
        outlined
        dense
        :label="t('credit')"
        mask="# (#-#-#)"
        :rules="[requireField]"
      />
      <q-input
        style="min-width: 200px"
        v-model="store.form.thaiName"
        outlined
        dense
        :label="t('name')"
        :rules="[requireField]"
      />
      <q-input
        style="min-width: 200px"
        v-model="store.form.engName"
        outlined
        dense
        :label="t('engName')"
        :rules="[requireField]"
      >
        <template #after></template>
      </q-input>

      <q-input
        class="col-12"
        v-model="store.form.thaiDescription"
        outlined
        dense
        type="textarea"
        :label="t('description')"
        :rules="[requireField]"
      />
      <q-input
        class="col-12"
        v-model="store.form.engDescription"
        outlined
        dense
        type="textarea"
        :label="t('engDescription')"
        :rules="[requireField]"
      />
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import DialogForm from 'src/components/form/DialogForm.vue';
import FieldChecker from 'src/components/form/FieldChecker.vue';
import { useSubjectStore } from 'src/stores/subject';
import { OptionSubjectType } from 'src/types/education';
import { requireField } from 'src/utils/field-rules';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const store = useSubjectStore();
</script>

<style scoped></style>
