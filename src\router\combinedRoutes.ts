import { EnumUserRole } from 'src/data/roles';
import type { RouteRecordRaw } from 'vue-router';

export const routes: RouteRecordRaw[] = [
  // Dashboard routes - shared across all roles
  {
    path: '/dashboard',
    meta: {
      icon: 'home',
      title: 'Home',
      roles: [
        EnumUserRole.ADMIN,
        EnumUserRole.COORDINATOR,
        EnumUserRole.INSTRUCTOR,
        EnumUserRole.STUDENT,
      ],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        name: 'dashboard',
        path: '', // This makes it the default child route
        component: () => import('src/pages/DashboardPage.vue'),
      },
    ],
  },

  // Skills management - accessible by admin, coordinator, instructor
  {
    path: '/skills',
    meta: {
      icon: 'code',
      title: 'Skills',
      roles: [
        EnumUserRole.ADMIN,
        EnumUserRole.COORDINATOR,
        EnumUserRole.INSTRUCTOR,
      ],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '', // This makes it the default child route
        name: 'skills',

        component: () => import('src/pages/common/SkillPage.vue'),
      },
    ],
  },

  // Subjects management - admin only
  {
    path: '/subjects',
    meta: {
      icon: 'books',
      title: 'Subjects',
      roles: [EnumUserRole.ADMIN],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '', // This makes it the default child route
        name: 'subjects',

        component: () => import('src/pages/SubjectPage.vue'),
      },
    ],
  },

  // Curriculums management - admin and coordinator
  {
    path: '/curriculums/management',
    meta: {
      icon: 'school',
      title: 'Curriculums',
      roles: [EnumUserRole.ADMIN, EnumUserRole.COORDINATOR],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '', // This makes it the default child route
        name: 'curriculums-management',
        component: () => import('src/pages/CurriculumsPage.vue'),
      },
    ],
  },

  // Courses management - all roles except student
  // {
  //   path: '/courses',
  //   meta: {
  //     icon: 'play_lesson',
  //     title: 'Courses',
  //     roles: [
  //       EnumUserRole.ADMIN,
  //       EnumUserRole.COORDINATOR,
  //       EnumUserRole.INSTRUCTOR,
  //       EnumUserRole.STUDENT,
  //     ],
  //   },
  //   component: () => import('src/layouts/AdminLayout.vue'),
  //   children: [
  //     {
  //       path: '',
  //       name: 'courses',
  //       component: () => import('src/features/curriculum/tabs/course/CoursePage.vue'),
  //       children: [
  //         {
  //           path: ':id',
  //           name: 'course-details',
  //           meta: {
  //             roles: [
  //               EnumUserRole.ADMIN,
  //               EnumUserRole.COORDINATOR,
  //               EnumUserRole.INSTRUCTOR,
  //               EnumUserRole.STUDENT,
  //             ],
  //           },
  //           component: () => import('src/features/curriculum/tabs/course/CourseDetails.vue'),
  //         },
  //       ],
  //     },
  //   ],
  // },

  // User management - admin only
  {
    path: '/users',
    meta: {
      icon: 'people',
      title: 'Users',
      roles: [EnumUserRole.ADMIN],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '', // This makes it the default child route
        name: 'users',
        component: () => import('src/pages/UserPage.vue'),
      },
    ],
  },

  // Student management - admin only
  {
    path: '/students',
    meta: {
      icon: 'school',
      title: 'Students',
      roles: [EnumUserRole.ADMIN],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'students',
        component: () => import('src/pages/student/StudentPage.vue'),
      },
    ],
  },

  // Instructor management - admin only
  {
    path: '/instructors/management',
    meta: {
      icon: 'person',
      title: 'Instructors',
      roles: [EnumUserRole.ADMIN],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '', // This makes it the default child route
        name: 'instructors',
        component: () => import('src/pages/InstructorPage.vue'),
      },
    ],
  },

  // Faculty management - admin onlyP
  {
    path: '/faculties',
    meta: {
      icon: 'business',
      title: 'Faculties',
      roles: [EnumUserRole.ADMIN],
    },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: '', // This makes it the default child route
        name: 'faculties',
        component: () => import('src/pages/FacultyPage.vue'),
      },
    ],
  },
];

export default routes;
