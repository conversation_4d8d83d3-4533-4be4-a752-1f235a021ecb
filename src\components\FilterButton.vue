<template>
  <q-btn
    icon="filter_alt"
    flat
    color="primary"
    dense
    padding="none"
    @click="initOptions"
  >
    <q-tooltip anchor="top middle" :offset="[0, 30]">{{
      t('filter')
    }}</q-tooltip>
    <transition
      appear
      enter-active-class="animated bounceIn"
      leave-active-class="animated bounceOut"
    >
      <q-badge
        v-if="computedFilterModel"
        color="red"
        floating
        class="q-ml-sm"
        size="sm"
      ></q-badge>
    </transition>

    <q-popup-proxy class="q-pa-lg" v-model="proxyModel" style="width: 400px">
      <div class="flex justify-between">
        <div class="text-body1 text-primary text-bold">{{ t('filter') }}</div>
        <q-btn icon="close" flat padding="none" @click="closeMenu"></q-btn>
      </div>
      <q-separator class="q-my-sm" color="primary"></q-separator>
      <transition-group
        enter-active-class="animated bounceIn"
        leave-active-class="animated fadeOut"
      >
        <q-chip
          key="faculty"
          v-if="filterStore.filters[filterStore.page]?.facultyId"
          :label="modelFaculty?.thaiName"
          removable
          @remove="removeFacultyFilter"
        />
        <q-chip
          key="branch"
          v-if="filterStore.filters[filterStore.page]?.branchId"
          :label="modelBranch?.thaiName"
          removable
          @remove="removeBranchFilter"
        />
        <q-chip
          v-for="item in selectedCodeYears"
          :label="t('filters.yearCode') + ' ' + item"
          :key="item"
          removable
          @remove="removeCodeYearsFilter(item)"
        />
      </transition-group>
      <div class="q-gutter-md q-mt-md">
        <!-- <q-select
          v-if="filterStore.isShowFilter('faculty')"
          v-model="modelFaculty"
          :label="t('faculty')"
          outlined
          use-input
          option-label="thaiName"
          hide-selected
          fill-input
          @filter="filterFaculty"
          input-debounce="0"
          :options="facultyOptions"
          @update:model-value="updateModelFaculty"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                {{ t('noFaculty') }}
              </q-item-section>
            </q-item>
          </template>
        </q-select> -->
        <!-- <q-select
          v-if="filterStore.isShowFilter('branch')"
          :disable="!filterStore.filters[filterStore.page]?.facultyId"
          v-model="modelBranch"
          :label="t('branch')"
          fill-input
          option-label="thaiName"
          hide-selected
          outlined
          input-debounce="0"
          use-input
          @filter="filterBranch"
          @update:model-value="updateModelBranch"
          :options="branchOptions"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                {{ t('noBranch') }}
              </q-item-section>
            </q-item>
          </template>
        </q-select> -->
        <!-- Student Code Year -->
        <q-select
          v-if="filterStore.isShowFilter('codeYear')"
          :disable="studentCodeYears.length === 0"
          v-model="selectedCodeYears"
          :label="t('academicYear')"
          multiple
          fill-input
          clearable
          outlined
          input-debounce="0"
          use-input
          :options="studentCodeYears"
          @update:model-value="updateCodeYears"
        />
        <!-- Academic Years -->
        <q-select
          v-if="filterStore.isShowFilter('semesterYear')"
          v-model="selectedYear"
          :label="t('academicYear')"
          outlined
          multiple
          input-debounce="0"
          :options="getYears()"
          @update:model-value="updateYears"
        />
        <q-select
          v-if="filterStore.isShowFilter('semesterYear')"
          v-model="selectedSemester"
          :label="t('semester')"
          multiple
          outlined
          input-debounce="0"
          :options="[1, 2, 3]"
          @update:model-value="updateSemester"
        />
        <div class="q-gutter-sm">
          <div>{{ t('searchBy') }}</div>
          <q-radio
            v-for="o in filterStore.getSearchOptions[filterStore.page]"
            v-model="filterStore.searchBy"
            :label="t('searchByIndex.' + o)"
            :val="o"
            :key="o"
            checked-icon="task_alt"
          ></q-radio>
        </div>
      </div>
      <div class="row q-gutter-x-md q-mt-md">
        <q-btn
          class="col-grow"
          :label="t('clear')"
          flat
          color="negative"
          @click="resetFilter"
        ></q-btn>
        <q-btn
          class="col-grow"
          :label="t('confirm')"
          flat
          color="primary"
          @click="applyFilter"
        ></q-btn>
      </div>
    </q-popup-proxy>
  </q-btn>
</template>

<script lang="ts" setup>
import { api } from 'src/boot/axios';
import useFilterStore from 'src/stores/filter-search';
import type { Faculty, Branch } from 'src/types/education';
import { getYears } from 'src/utils/years';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const filterStore = useFilterStore();
const faculties = ref<Faculty[]>();
const proxyModel = ref(false);
const strFacultyOptions = ref<string[]>([]);
const strBranchOptions = ref<string[]>([]);
const facultyOptions = ref<string[]>([]);
const branchOptions = ref<string[]>([]);
const studentCodeYears = ref<string[]>([]);

const selectedCodeYears = ref<string[]>([]);
const selectedYear = ref<number[]>();
const selectedSemester = ref<number[]>([]);

const modelFaculty = defineModel<Partial<Faculty> | null | undefined>(
  'model-faculty',
  {
    default: undefined,
  },
);
const modelBranch = defineModel<Partial<Branch> | null | undefined>(
  'model-branch',
  {
    default: undefined,
  },
);
const modelCodeYears = defineModel<string | number | null | undefined>(
  'model-code-years',
  {
    default: undefined,
  },
);

// Initialize options
const initOptions = async () => {
  const { data } = await api.get<Faculty[]>('/faculties/filters');
  if (data && faculties.value?.length !== data.length) {
    faculties.value = data;
    strFacultyOptions.value = data.map((f) => f.thaiName);
    strBranchOptions.value = data.flatMap(
      (f) => f.branch?.map((b) => b.thaiName || '') || [],
    );
    facultyOptions.value = [...strFacultyOptions.value];
    branchOptions.value = [...strBranchOptions.value];

    if (filterStore.isShowFilter('codeYear')) {
      await fetchCodeYears();
    }
  }
};

// Handle faculty change
// const updateModelFaculty = async (val: string) => {
//   const index = faculties.value?.findIndex((f) => f.thaiName === val);
//   if (index !== undefined && index > -1) {
//     modelFaculty.value = faculties.value?.[index];

//     filterStore.filters[filterStore.page] = {
//       ...filterStore.filters[filterStore.page],
//       facultyId: modelFaculty.value?.id,
//     };

//     await nextTick();
//     // Reset branchName BEFORE updating options
//     delete filterStore.filters[filterStore.page]?.branchId;

//     // ✅ รีเซ็ตปีรหัสที่เลือกไว้ก่อนหน้า

//     strBranchOptions.value =
//       faculties.value?.[index]?.branch?.map((b) => b.thaiName || '') || [];
//     branchOptions.value = [...strBranchOptions.value];

//     if (filterStore.isShowFilter('codeYear')) {
//       await fetchCodeYears(modelFaculty.value?.id);
//       delete filterStore.filters[filterStore.page].codeYears;
//       // remove selected code years if not in student code years
//       selectedCodeYears.value = selectedCodeYears.value.filter((codeYear) =>
//         studentCodeYears.value.includes(codeYear),
//       );
//     }
//   } else {
//     delete filterStore.filters[filterStore.page]?.branchId;
//     strBranchOptions.value = [];
//     branchOptions.value = [];
//   }
// };

const updateYears = (arr: number[]) => {
  filterStore.filters[filterStore.page].years = arr;
};

const updateCodeYears = (arr: string[]) => {
  filterStore.filters[filterStore.page].codeYears = arr;
};

const updateSemester = (arr: number[]) => {
  filterStore.filters[filterStore.page] ??= {};
  filterStore.filters[filterStore.page].semesters = arr;
};

const removeCodeYearsFilter = (val: string | number) => {
  if (!val) {
    return;
  }
  val = val.toString();

  // Remove the year from the selected set
  if (selectedCodeYears.value?.includes(val)) {
    selectedCodeYears.value.splice(selectedCodeYears.value.indexOf(val), 1);

    filterStore.filters[filterStore.page].codeYears = Array.from(selectedCodeYears.value);
  }
};

// Filter faculty options
// const filterFaculty = (
//   val: string,
//   callback: (updateFn: () => void) => void,
// ) => {
//   callback(() => {
//     const needle = val.toLowerCase();
//     facultyOptions.value = strFacultyOptions.value.filter((v) =>
//       v.toLowerCase().includes(needle),
//     );
//   });
// };

// Set branch model
// const updateModelBranch = async (val: string) => {
//   const index = strBranchOptions.value?.findIndex((f) => f === val);
//   if (index !== undefined && index > -1) {
//     modelBranch.value = modelFaculty.value?.branch![index];
//     filterStore.filters[filterStore.page] = {
//       ...filterStore.filters[filterStore.page],
//       branchId: modelBranch.value?.id,
//     };

//     if (filterStore.isShowFilter('codeYear')) {
//       await fetchCodeYears(modelFaculty.value?.id, modelBranch.value?.id);
//       delete filterStore.filters[filterStore.page].codeYears;
//       selectedCodeYears.value = selectedCodeYears.value.filter((codeYear) =>
//         studentCodeYears.value.includes(codeYear),
//       );
//     }

//     await nextTick();
//   }
// };

const fetchCodeYears = async (
  facultyId?: number,
  branchId?: number,
  curriculumId?: number,
) => {
  if (!filterStore.isShowFilter('codeYear')) return;
  const { data } = await api.get<string[]>('/students/code-year', {
    params: {
      facultyId,
      branchId,
      curriculumId,
    },
  });
  if (data) {
    studentCodeYears.value = data;
  }
};

// Filter branch options
// const filterBranch = (
//   val: string,
//   callback: (updateFn: () => void) => void,
// ) => {
//   callback(() => {
//     const needle = val.toLowerCase();
//     branchOptions.value = strBranchOptions.value.filter((v) =>
//       v.toLowerCase().includes(needle),
//     );
//   });
// };

// Computed properties
const computedFilterModel = computed(() => {
  if (filterStore.filters[filterStore.page]?.facultyId || filterStore.filters[filterStore.page]?.branchId) {
    return filterStore.filters[filterStore.page];
  }
  return null;
});

// Reset filters
const resetFilter = () => {
  filterStore.filters[filterStore.page] = {};
  branchOptions.value = [...strBranchOptions.value];
  facultyOptions.value = [...strFacultyOptions.value];
  selectedCodeYears.value = [];
  modelFaculty.value = undefined;
  modelBranch.value = undefined;
  modelCodeYears.value = undefined;
  selectedCodeYears.value = [];
  selectedYear.value = [];
  selectedSemester.value = [];
  emit('confirmFilter');
};

// Remove specific filters
const removeFacultyFilter = () => {
  modelFaculty.value = undefined;
  delete filterStore.filters[filterStore.page]?.facultyId;
  resetFilter();
};

const removeBranchFilter = () => {
  modelBranch.value = undefined;
  delete filterStore.filters[filterStore.page]?.branchId;
};

// Close menu
const closeMenu = () => {
  proxyModel.value = false;
  resetFilter();
};

// Apply filters
const applyFilter = () => {
  proxyModel.value = false;
  emit('confirmFilter');
};

// Emit event
const emit = defineEmits<(e: 'confirmFilter') => void>();

</script>
