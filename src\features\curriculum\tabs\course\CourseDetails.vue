<template>
  <q-page >
    <q-breadcrumbs>
      <q-breadcrumbs-el :label="t('courses')" :to="`/curriculums/${route.params.code}/courses`" />
      <q-breadcrumbs-el :label="`${store.getCourseId}`" />
    </q-breadcrumbs>
    <q-separator class="q-my-md" />

    <!-- Top Card -->
    <section class="row q-gutter-md">
      <q-card flat bordered class="col-grow col-sm q-animate--fade">
        <q-card-section>
          <div class="text-h6">
            {{ t('subjectId') }} {{ store.getCourse.subject?.id }}
          </div>
          <div class="text-h5">
            {{ store.getCourse.subject?.thaiName }}
          </div>
        </q-card-section>
      </q-card>
    </section>

    <!-- Table CLO + Skill -->
    <q-card flat bordered class="q-my-md q-animate--fade">
      <q-table :rows="store.getRowClo" :columns="columns">
        <template #body-cell-number="props">
          <q-td>{{ props.rowIndex + 1 }}</q-td>
        </template>
        <template #body-cell-actions="props">
          <q-td class="q-gutter-x-xs" style="min-width: 120px">
            <q-btn
              unelevated
              color="grey-8"
              class="text-caption"
              dense
              icon="upload"
              @click="openUpdateScoreDialog(props.row)"
            />
          </q-td>
        </template>
      </q-table>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { useMeta, useQuasar, type QTableColumn } from 'quasar';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useCourseStore } from 'src/stores/course';
import UpdateScoreDialog from 'src/features/curriculum/tabs/course/dialog/UpdateScoreDialog.vue';
import type { CLO } from 'src/types/education';

const $q = useQuasar();
const store = useCourseStore();
const route = useRoute();
const { t } = useI18n();

const openUpdateScoreDialog = (clo: CLO) => {
  store.selectedClo = clo;
  $q.dialog({
    component: UpdateScoreDialog,
  });
};

onMounted(async () => {
  await store.fetchCourseId(route.params.id as string);
});

useMeta({
  title: store.getCourse.subject?.thaiName,
});
const columns: QTableColumn[] = [
  {
    name: 'number',
    label: t('no.'),
    field: '',
    align: 'left',
  },
  {
    name: 'clo',
    label: `CLO ${t('thaiDescription')}`,
    field: (r) => r.thaiDescription,
    align: 'left',
  },
  {
    name: 'skill',
    label: t('skills'),
    field: (r) => r.skill.thaiName || r.skill.engName,
    align: 'left',
  },
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'left',
  },
];
</script>
