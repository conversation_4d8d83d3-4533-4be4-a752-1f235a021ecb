<template>
  <div class="q-gutter-y-md q-mt-md" v-if="show">
    <PageTitle :title="t('ประวัติการให้คะแนน')" />

    <div class="row q-gutter-md">
      <div>
        <q-input
          outlined
          autofocus
          clearable
          v-model="searchText"
          label="ค้นหา"
          class="col"
          bg-color="white"
          dense
          style="width: 300px"
        >
          <template #prepend>
            <q-icon name="search"></q-icon>
          </template>
        </q-input>
      </div>
      <q-select
        dense
        clearable
        v-model="selectedSubject"
        :options="subjectOptions"
        label="วิชา"
        style="width: 150px; margin-bottom: 20px"
      />
      <q-select
        dense
        clearable
        v-model="selectedYear"
        :options="yearOptions"
        label="รหัสปีการศึกษา"
        style="width: 150px; margin-bottom: 20px"
      />

    </div>

    <!-- Table -->
    <div style="overflow-x: auto; width: 100%">
      <q-table
        flat
        bordered
        class="q-animate--fade q-mt-md"
        separator="cell"
        :rows="rowsData"
        row-key="code"
        :columns="skillCollectionColumns"
      >
        <template #body-cell-number="props">
          <q-td>{{ props.rowIndex + 1 }}</q-td>
        </template>
      </q-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
/*
    imports
*/
import { useI18n } from 'vue-i18n';
import PageTitle from 'src/components/common/PageTitle.vue';
import { computed, onMounted, ref, watch } from 'vue';
import type { QTableColumn } from 'quasar/dist/types/api.js';
import { LearningDomain, type Skill, type SkillCollectionSummary } from 'src/types/education';

import { useCurriculumStore } from 'src/stores/curriculum';
import { CurriculumService } from 'src/services/curriculums';
import { useSubjectStore } from 'src/stores/subject';
import useFilterStore from 'src/stores/filter-search';
import { useStudentStore } from 'src/stores/student';

/*
    states
*/
const curriculumId = useCurriculumStore().getInsertId ?? -1;
const subjectStore = useSubjectStore();
const filterStore = useFilterStore();
const studentStore = useStudentStore();

const skills = ref<Skill[]>([]);
const rowsData = ref<SkillCollectionSummary[]>([]);
const { t } = useI18n();
const searchText = ref('');
const selectedYear = ref('');
const yearOptions = computed(() => {
  const years = studentStore.students.map((s) => s.code.slice(0, 2));
  return [...new Set(years)].sort();
});
const selectedSubject = ref('');
const subjectOptions = computed(() =>
  subjectStore.subjects.map((s) => s.thaiName),
);
const show = ref(false);

// ฟังก์ชันดึง skill level
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getSkillLevel(row: any, skillName: string): string {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const match = row.skills?.find((s: any) => s.skillName === skillName); // เปลี่ยนเป็น s.skillName
  return match
    ? match.gainedLevel !== null
      ? match.gainedLevel.toString()
      : '-'
    : '-';
}
function extractSkillsFromStudents(): Skill[] {
  const skillMap = new Map<string, Skill>();

  rowsData.value.forEach((s) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    s.skills?.forEach((sk: any) => {
      if (sk.skillName && !skillMap.has(sk.skillName)) {
        skillMap.set(sk.skillName, {
          id: 0,
          thaiName: sk.skillName,
          engName: '',
           domain: LearningDomain.Psychomotor,
          curriculumId: 0,
        });
      }
    });
  });

  return Array.from(skillMap.values());
}

async function fetchData() {
  // const res1 = await SkillService.getOptions(curriculumId);
  // skills.value = res1.data;

  const res2 = await CurriculumService.getSummarySkillCollection(
    curriculumId,
    selectedYear.value,       // ส่งเป็น studentCode
    selectedSubject.value,    // ส่งเป็น subjectName
    searchText.value          // ส่งเป็น studentName
  );
  rowsData.value = res2.students;

  await subjectStore.fetchAllInCurr();
  filterStore.updateFilter({ curriculumId: curriculumId });
  studentStore.pagination = { rowsPerPage: 1000 };
  await studentStore.fetchAll(studentStore.pagination);
  show.value = true;
}

onMounted(async () => {
  try {
    await fetchData();
  } catch (error) {
    console.error('Error loading skill options:', error);
  }
  console.log(subjectStore.subjects);
});
watch([selectedYear, selectedSubject, searchText], async () => {
  try {
    await fetchData();
  } catch (err) {
    console.error('Error reloading filtered data:', err);
  }
});
watch(rowsData, () => {
  skills.value = extractSkillsFromStudents();
});


const skillCollectionColumns = computed<QTableColumn[]>(() => [
  {
    name: 'number',
    label: t('no.'),
    field: () => {},
    align: 'left',
  },
  {
    name: 'studentCode',
    label: t('studentCode'),
    field: 'studentCode',
    align: 'left',
  },
  {
    name: 'studentName',
    label: t('name'),
    field: 'studentName',
    align: 'left',
  },
  ...skills.value.map((skill) => ({
    name: `skill_${skill.thaiName}`,
    label: skill.thaiName,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    field: (row: any): string => getSkillLevel(row, skill.thaiName),
    align: 'center' as const,
  })),
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'left',
  },
]);


</script>

<style scoped>
.q-table__container {
  overflow-x: auto;
  max-width: 100%;
}

.q-table thead th,
.q-table tbody td {
  white-space: nowrap;
}
</style>
