<template>
  <DialogForm
    :title="store.formBranch.id ? t('form.Edit Branch') : t('form.New Branch')"
    v-model="store.dialogState"
    @save="store.handleSave"
    :cta-text="computedCtaText"
    :json="store.getJsonForm"
  >
    <div class="q-gutter-y-md">
      <q-input
        outlined
        dense
        :label="t('name')"
        v-model="store.formBranch.thaiName"
        :rules="[requireField]"
      />
      <q-input
        outlined
        dense
        :label="t('engName')"
        v-model="store.formBranch.engName"
        :rules="[requireField]"
      />
      <q-input
        outlined
        dense
        :label="t('abbrev')"
        hint="Optional"
        v-model="store.formBranch.abbrev"
      />
      <q-input
        outlined
        dense
        :label="t('description')"
        type="textarea"
        counter
        :rules="[requireField]"
        maxlength="1000"
        v-model="store.formBranch.thaiDescription"
      />
      <q-input
        outlined
        dense
        :label="t('englishDescription')"
        hint="Optional"
        type="textarea"
        counter
        maxlength="1000"
        v-model="store.formBranch.engDescription"
      />
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import { useFacultyStore } from 'src/stores/faculty-branch';
import { requireField } from 'src/utils/field-rules';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DialogForm from 'src/components/form/DialogForm.vue';

const store = useFacultyStore();
const { t } = useI18n();

const computedCtaText = computed(() => {
  if (store.titleForm === 'form.New Faculty') {
    return 'createFaculty';
  } else if (store.titleForm === 'form.New Branch') {
    return 'createBranch';
  }
  return '';
});
</script>

<style scoped></style>
