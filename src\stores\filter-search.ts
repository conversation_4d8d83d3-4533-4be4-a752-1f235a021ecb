import { defineStore, getActivePinia } from 'pinia';
import type { QueryParams } from 'src/types/api';

type FilterNames = 'faculty' | 'branch' | 'codeYear' | 'semesterYear';
type SearchNames = 'nameCode' | 'skill';
type PageNames =
  | 'students'
  | 'courses'
  | 'student_curriculum'
  | 'skills'
  | 'subjects'
  | 'instructors';

export type { FilterNames, SearchNames, PageNames };

interface FilterState {
  search: string;
  searchBy: SearchNames;
  showFilters: { [key in PageNames]: FilterNames[] };
  searchOptions: { [key in PageNames]: SearchNames[] };
  filters: { [key in PageNames]: Partial<QueryParams> };
  page: PageNames;
}

const useFilterStore = defineStore('filter', {
  state: (): FilterState => {
    return {
      search: '',
      searchBy: 'nameCode',
      searchOptions: {
        students: ['nameCode', 'skill'],
        courses: ['nameCode'],
        subjects: ['nameCode'],
        student_curriculum: ['nameCode'],
        skills: ['nameCode'],
        instructors: ['nameCode'],
      },
      showFilters: {
        students: ['faculty', 'branch', 'codeYear'],
        courses: ['faculty', 'branch', 'semesterYear'],
        student_curriculum: ['codeYear'],
        skills: [],
        subjects: [],
        instructors: [],
      },
      filters: {
        students: {},
        courses: {},
        student_curriculum: {},
        skills: {},
        subjects: {},
        instructors: {},
      },
      page: 'students',
    };
  },
  getters: {
    getSearch: (state) => state.search,
    getSearchBy: (state) => state.searchBy,
    getSearchOptions: (state) => state.searchOptions,
    getShowFilters: (state) => state.showFilters,
    getFilters: (state) => state.filters,
    getQuery: (state) => state.filters[state.page],
    getCurrentPageFilters: (state) => state.filters[state.page] || {},
  },
  actions: {
    setPage(page: PageNames) {
      this.page = page;
    },
    setFilter(value: QueryParams) {
      this.filters[this.page] = value;
    },
    updateFilter(value: Partial<QueryParams>) {
      this.filters[this.page] = Object.assign(this.filters[this.page], value);
    },
    setSearchBy(value: SearchNames) {
      this.searchBy = value;
    },
    clearFilter() {
      this.filters[this.page] = Object.assign({});
    },
    isShowFilter(name: FilterNames) {
      return this.showFilters[this.page].includes(name);
    },
  },
});

function initializeFilterStoreSubscription() {
  if (!getActivePinia()) {
    return;
  }

  useFilterStore().$subscribe((mutation, state) => {
    if (!state.page) return;

    // Ensure filters object exists for current page
    if (!state.filters[state.page]) {
      state.filters[state.page] = {};
    }

    // global search
    switch (state.searchBy) {
      case 'nameCode':
        delete state.filters[state.page].skillName;
        if (state.search.trim()) {
          state.filters[state.page].nameCode = state.search.trim();
        } else {
          delete state.filters[state.page].nameCode;
        }
        break;
      case 'skill':
        delete state.filters[state.page].nameCode;
        if (state.search.trim()) {
          state.filters[state.page].skillName = state.search.trim();
        } else {
          delete state.filters[state.page].skillName;
        }
        break;
    }
  });
}

initializeFilterStoreSubscription();

export default useFilterStore;
