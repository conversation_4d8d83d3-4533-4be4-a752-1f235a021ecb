<template>
  <q-page padding>
    <PageHeader
      v-model:search-text="filterStore.filters['subjects'].nameCode"
      hide-filter
      hide-add-btn
      :label-search="t('search') + t('subject')"
      @enter-search="store.fetchAll"
    />
    <!-- Table -->
    <q-table
      flat
      bordered
      :loading="global.getLoadingState"
      v-model:pagination="store.pagination"
      class="q-mt-md q-animate--fade page-table"
      :rows="store.getListSubjects"
      :columns="subjectColumns"
      row-key="id"
      wrap-cells
      separator="cell"
      @update:pagination="store.fetchAll"
      @request="(r) => store.fetchAll(r.pagination)"
    >
      <template #body-cell-description="props">
        <q-td style="max-width: 300px">
          {{ props.value }}
        </q-td>
      </template>
      <template #body-cell-credit="props">
        <q-td style="min-width: 90px">
          {{ props.value }}
        </q-td>
      </template>
      <template #body-cell-curriculums="props">
        <q-td style="min-width: 90px">
          <q-item
            clickable
            @click="goTo(item.curriculum.code)"
            dense
            v-for="item in props.value"
            :key="item.curriculum.code"
            >{{ item.curriculum.code }}
          </q-item>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script lang="ts" setup>
import { useMeta } from 'quasar';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useGlobalStore } from 'src/stores/global';
import PageHeader from 'src/components/common/PageHeader.vue';
import { useI18n } from 'vue-i18n';
import { useSubjectStore } from 'src/stores/subject';
import { subjectColumns } from 'src/data/column_table';
import useFilterStore from 'src/stores/filter-search';

const { t } = useI18n();
const global = useGlobalStore();
const route = useRoute();
const router = useRouter();
const title = computed(() => route.matched[1]?.name as string);
const store = useSubjectStore();
const filterStore = useFilterStore();

const goTo = async (code: string) => {
  await router.push(`/curriculums/${code}/subjects`);
};

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>
