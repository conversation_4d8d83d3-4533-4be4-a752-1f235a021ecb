import { api } from 'boot/axios';
import type { Curriculum } from 'src/types/education';
import type { DataResponse, QueryParams } from 'src/types/api';
export class CurriculumService {
  static path = 'curriculums';
  static async getAll(p?: Partial<QueryParams>) {
    const res = await api.get<DataResponse<Curriculum>>(this.path, {
      params: p,
    });
    return res.data;
  }

  static async getOneByCode(code: string) {
    const res = await api.get(`${this.path}/${code}`);
    return res.data;
  }

  static async createOne(obj: Partial<Curriculum>) {
    delete obj.lessons;
    delete obj.coordinators;
    const res = await api.post(this.path, obj);
    return res.data;
  }

  static async updateOne(obj: Partial<Curriculum>) {
    // dto validation
    delete obj.plos;
    delete obj.lessons;
    delete obj.subjects;
    delete obj.branch;
    delete obj.skills;
    delete obj.coordinators;
    const res = await api.patch(`${this.path}/${obj.id}`, obj);
    return res.data;
  }

  static async removeOne(id: number) {
    const res = await api.delete(`${this.path}/${id}`);
    return res.data;
  }

  static async getSummary(id: number, yearCode?: string, skillType?: string) {
    const res = await api.get(
      `/curriculums/summary${id}`,
      {
        params: {
          yearCode,
          skillType
        }
      }
    );
    return res.data;
  }

  static async getStudentSummary(skillId: number, yearCode?: string, targetLevel?: string) {
    const res = await api.get(
      `${this.path}/filters/skill/${skillId}/students`,
      {
        params: {
          targetLevel,
          yearCode
        }
      }
    );
    return res.data;
  }

  static async getSummarySkillCollection(id: number, studentCode?: string, subjectName?: string, studentName?: string) {
    const res = await api.get(`/curriculums/summary/skill-collection/${id}`,
      {
        params: {
          studentCode,
          subjectName,
          studentName
        }
      }
    );
    return res.data;
  }
}
