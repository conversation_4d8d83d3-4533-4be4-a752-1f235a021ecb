import { defineStore } from 'pinia';
import SkillService from 'src/services/skill';
import type { QTableProps } from 'quasar';
import {
  calMaxPage,
  convertToQuery,
  defaultPagination,
  handleDeleteWithPagination,
  mergeOrUpdateReactiveArray,
} from 'src/utils/pagination';
import { useCurriculumStore } from './curriculum';
import useFilterStore from './filter-search';
import type { Skill } from 'src/types/education';
import { Dialog, Notify } from 'quasar';
import { i18n } from 'src/boot/i18n';

const { t } = i18n.global;

export type TitleFormSkill =
  | 'form.New Skill'
  | 'form.Edit Skill'
  | 'form.Delete Skill'
  | 'Insert Sub-Skill'
  | 'form.View';

export const useSkillStore = defineStore('skill', {
  state: () => ({
    selectedItem: {} as Skill,
    skills: [] as Skill[],
    form: {} as Partial<Skill>,
    dialogForm: false,
    pagination: { ...defaultPagination },
    titleForm: 'form.New Skill' as TitleFormSkill,
    parent: {} as Skill,
    total: 0,
    onlyHaveSubs: false,
    curr: useCurriculumStore(),
    lastFetchedCurriculumId: 0,
  }),
  getters: {
    getSelectedItem: (state) => {
      if (Object.keys(state.selectedItem).length > 0) {
        return state.selectedItem;
      }
    },
    getTitleForm: (state) => state.titleForm,
    getParentId: (state) => state.parent.id,
    getParentName: (s) => s.parent.thaiName,
    getMaxPage: (state) => {
      if (!state || typeof state.total !== 'number') {
        return 1;
      }

      const rowsPerPage = state.pagination?.rowsPerPage;
      return calMaxPage(state.total, rowsPerPage);
    },
    getSkills: (state) => {
      if (state.onlyHaveSubs) {
        const filtered = state.skills.filter(
          (sk) => sk.subs !== undefined && sk.subs?.length > 0,
        );
        return filtered;
      }
      return state.skills;
    },
    getSubjects: (s) => s.skills,
  },
  actions: {
    async fetchAll(pag?: QTableProps['pagination']) {
      const filterStore = useFilterStore();
      const { data, total } = await SkillService.getAll(
        convertToQuery(pag || this.pagination, filterStore.getQuery),
      );
      this.skills = JSON.parse(JSON.stringify(data));
      this.total = total;
    },
    async fetchAllInCurr(
      pag?: QTableProps['pagination'],
      curriculumId?: number | string,
    ): Promise<{ data: Skill[]; total: number }> {
      const filterStore = useFilterStore();
      filterStore.setPage('skills');
      const currId = this.curr.getInsertId || curriculumId;
      if (!currId) {
        console.warn('No curriculum code found in route params.');
        return { data: [], total: 0 };
      }
      const currentPagination = pag || this.pagination;
      this.pagination = currentPagination;

      filterStore.filters.skills.curriculumId = currId;

      try {
        // Ensure we're using the latest pagination settings when fetching
        const { data, total } = await SkillService.getAll(
          convertToQuery(this.pagination, filterStore.getQuery),
        );

        //merge new items
        this.skills = mergeOrUpdateReactiveArray(this.skills, data);

        // Sort skills array to show most recently added first if on page 1
        if (this.pagination.page === 1) {
          this.skills.sort((a, b) => b.id - a.id);
        }

        // Update state
        this.total = total;

        return { data, total };
      } catch (error) {
        console.error('Failed to fetch skills:', error);
        return { data: [], total: 0 };
      }
      console.log('🚀 ~ filterStore.getQuery:', filterStore.getQuery);
    },

    // independent skill
    async handleSave() {
      const curId = this.curr.getInsertId;
      this.form = {
        ...this.form,
        curriculumId: curId,
      } as Partial<Skill>;

      if (this.parent) {
        const ok = await SkillService.addSubSkill(this.parent.id, this.form);
        if (ok)
          Notify.create({
            type: 'ok',
            message: `${t('notify.createSuccess')}`,
          });
      } else {
        if (this.form.id) {
          const index = this.skills.findIndex((s) => s.id === this.form.id);
          const ok = await SkillService.updateSkill(this.form.id, this.form);
          if (ok)
            Notify.create({
              type: 'ok',
              message: `${t('notify.updateSuccess')}`,
            });
          if (index !== -1) {
            this.pagination.page = Math.ceil(
              (index + 1) / this.pagination.rowsPerPage!,
            );
            console.log(index);
            console.log(this.pagination);
          }
        } else {
          const ok = await SkillService.addSkill(this.form);
          if (ok) {
            Notify.create({
              type: 'ok',
              message: `${t('notify.createSuccess')}`,
            });
            // Fetch the latest data with the newest skill
            // Reset to page 1 to ensure we see newly added items
            this.pagination.page = 1;
          }
        }
      }
      await this.fetchAllInCurr(this.pagination);
      this.dialogForm = false;
      this.resetForm();
    },
    handleRemove(skill: Skill) {
      Dialog.create({
        title: `${t('dialog.deleteConfirm')}`,
        message: `${t('dialog.deleteConfirm')} ${skill.thaiName}?`,
        cancel: true,
        persistent: true,
      })
        .onCancel(() => {
          return;
        })
        .onOk(() => {
          Notify.create({
            type: 'ok',
            message: `${t('notify.deleteSuccess')}`,
          });
          
          // Check if this is a sub-skill (has a parentId)
          if (skill.parentId) {
            // Handle sub-skill removal
            SkillService.removeSubSkill(skill.parentId, skill.id)
              .then(() => {
                // Update the store data after removing the sub-skill
                // Find the parent skill in the current data
                const parentIndex = this.skills.findIndex(s => s.id === skill.parentId);
                if (parentIndex !== -1 && this.skills[parentIndex].subs) {
                  // Remove the sub-skill from the parent's subs array
                  const subIndex = this.skills[parentIndex].subs.findIndex(s => s.id === skill.id);
                  if (subIndex !== -1) {
                    this.skills[parentIndex].subs.splice(subIndex, 1);
                  }
                }
                // Clear selection if the deleted item was selected
                if (this.selectedItem.id === skill.id) {
                  this.selectedItem = {} as Skill;
                }
              })
              .catch((error) => {
                console.error('Failed to remove sub-skill:', error);
              });
          } else {
            // Handle regular skill removal
            handleDeleteWithPagination<Skill>({
              id: skill.id,
              items: this.skills,
              total: this.total,
              pagination: this.pagination,
              deleteFn: SkillService.removeSkill,
              fetchFn: () => this.fetchAllInCurr(this.pagination),
              setTotal: (newTotal) => (this.total = newTotal),
              setPage: (newPage) => (this.pagination.page = newPage),
            })
              .then(() => {
                this.selectedItem = {} as Skill;
              })
              .catch(() => {});
          }
        });
    },
    toggleDialog({
      form,
      title,
      parent,
    }: {
      form?: Partial<Skill>;
      title?: TitleFormSkill;
      parent?: Partial<Skill>;
      index: number;
    }) {
      this.titleForm = title || 'form.New Skill';
      this.parent = (parent as Skill) || null;
      if (form) {
        // copy form
        this.form = JSON.parse(JSON.stringify(form));
      } else {
        this.resetForm();
      }
      this.dialogForm = !this.dialogForm;
    },
    resetForm() {
      this.form = {} as Partial<Skill>;
    },
    setSelectedItem(item: Skill) {
      this.selectedItem = item;
    },
  },
});
