<template>
  <q-dialog v-model="dialogState">
    <q-card style="min-width: 70%; min-height: 80%">
      <q-card-section>
        <div class="row justify-between">
          <div class="text-h4 text-primary">{{ t('cloManagement') }}</div>
          <q-btn
            fab-mini
            flat
            padding="none"
            icon="close"
            @click="() => (dialogState = false)"
          ></q-btn>
        </div>

        <q-separator
          class="q-my-sm bg-primary"
          style="height: 3px"
        ></q-separator>
      </q-card-section>

      <q-card-section class="q-pa-lg bg-grey-1">
        <div class="col-12 text-h6">
          {{ t('subjectCode') }} {{ props.subject.code }}
          <div class="text-body1 q-ml-lg"></div>
        </div>
        <div class="col-12 text-body1">
          {{ t('name') }} : {{ props.subject.thaiName }}
        </div>
        <div class="col-12 text-body1">
          {{ t('engName') }} : {{ props.subject.engName }}
        </div>
      </q-card-section>
      <q-card-section>
        <MainHeader
          v-model:search-text="filterStore.search"
          @open-dialog="onClickAdd"
          hide-filter
        />

        <q-table
          flat
          bordered
          :loading="global.getLoadingState"
          class="q-mt-md q-animate--fade"
          :rows="store.getListClo"
          :columns="columns"
          row-key="id"
          wrap-cells
          separator="cell"
        >
          <template #body-cell-number="props">
            <q-td>{{ props.rowIndex + 1 }}</q-td>
          </template>
          <template #body-cell-actions="props">
            <ActionsCell
              key="actions"
              :props="props"
              @handle-edit="editRow(props.row)"
              @handle-delete="removeOne(props.row.id)"
            />
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useGlobalStore } from 'src/stores/global';
import { useCloStore } from 'src/stores/clos';
import { useQuasar, type QTableColumn } from 'quasar';
import MainHeader from 'src/components/common/PageHeader.vue';
import { usePloStore } from 'src/stores/plo';
import { useSkillStore } from 'src/stores/skill';
import useFilterStore from 'src/stores/filter-search';

import ActionsCell from 'src/components/table/ActionsCell.vue';
import CloForm from './CloForm.vue';
import type { Subject, Skill, PLO, CLO } from 'src/types/education';

const $q = useQuasar();
const props = defineProps<{ subject: Subject }>();

const dialogState = ref();
const { t } = useI18n();
const global = useGlobalStore();
const store = useCloStore();
const ploStore = usePloStore();
const skillStore = useSkillStore();
const filterStore = useFilterStore();

const selectedSkill = computed({
  get: () => store.form.skill ?? null,
  set: (val: Skill) => {
    store.form.skill = val || store.form.skill;
  },
});

const selectedPlo = computed({
  get: () => store.form.plo ?? null,
  set: (val: PLO) => {
    store.form.plo = val || store.form.plo;
  },
});

const columns: QTableColumn[] = [
  { name: 'number', label: t('no.'), field: '', align: 'left' },
  { name: 'name', label: t('name'), field: 'name', align: 'left' },
  {
    name: 'thaiDescription',
    label: t('thaiDescription'),
    field: 'thaiDescription',
    align: 'left',
  },
  {
    name: 'engDescription',
    label: t('engDescription'),
    field: 'engDescription',
    align: 'left',
  },
  {
    name: 'actions',
    label: t('actions'),
    field: 'actions',
    align: 'left',
    sortable: false,
  },
];

const editRow = (row: CLO) => {
  store.form = JSON.parse(JSON.stringify(row)); // คัดลอกข้อมูลจากแถวที่เลือกไปยัง form
  selectedPlo.value = ploStore.getListPLO.find((plo) => plo.id === row.ploId)!;
  selectedSkill.value = skillStore.skills.find(
    (skill) => skill.id === row.skillId,
  )!;
  store.titleForm = 'form.Edit CLO';
  $q.dialog({
    component: CloForm,
    componentProps: { subject: props.subject },
  });
};

const removeOne = (rowId: number) => {
  $q.dialog({
    title: 'Confirm',
    message: 'Are you sure you want to delete this CLO?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    store
      .removeOne(rowId)
      .then(() => {})
      .catch(() => {});
  });
};

const onClickAdd = () => {
  store.form = {} as CLO;
  store.titleForm = 'form.New CLO';
  $q.dialog({
    component: CloForm,
    componentProps: { subject: props.subject },
  });
};

onMounted(async () => {
  //clear listCLO
  store.clos = [];
  await store.fetchAll(props.subject.id);
});
</script>
