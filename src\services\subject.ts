// import type { PageParams } from 'src/types/pagination';
import { api } from 'src/boot/axios';
import type { DataResponse, QueryParams } from 'src/types/api';
import type { Subject } from 'src/types/education';

export class SubjectService {
  static path = 'subjects';
  static currPath = 'curriculumId';

  static async getAll(params?: Partial<QueryParams>) {
    const res = await api.get<DataResponse<Subject>>(this.path, { params });
    return res.data;
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`);
    return res.data;
  }

  static async findExistSubjectCode(code: string) {
    const res = await api.get(`${this.path}/subject-code/${code}`);
    return res.data;
  }

  static async createOne(obj: Partial<Subject>) {
    // validation
    delete obj.lesson;
    delete obj.clos;
    const res = await api.post(this.path, obj);
    return res.data;
  }

  static async updateOne(id: number, obj: Partial<Subject>) {
    // validation
    delete obj.lesson;
    delete obj.clos;
    delete obj.curriculums;
    const res = await api.patch(`${this.path}/${id}`, obj);
    return res.data;
  }

  static async removeOne(id: number) {
    const res = await api.delete(`${this.path}/${id}`);
    return res.data;
  }

  static async getSubjectByCurriculums(id: number) {
    const { data } = await api.get(`subjects/filters/${id}`);
    return {
      data: data[0],
      total: data[1],
    };
  }
}
