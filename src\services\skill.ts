import { api } from 'boot/axios';
import type { DataResponse, QueryParams } from 'src/types/api';
import type { Skill } from 'src/types/education';
class SkillService {
  static path = 'skills';
  static addSkill = async (skill: Partial<Skill>) => {
    const res = await api.post(this.path, skill);
    return res.status;
  };

  static addSubSkill = async (parentId: number, skill: Partial<Skill>) => {
    skill.parentId = parentId;
    const res = await api.post(`${this.path}`, skill);
    return res.status;
  };
  static updateSkill = async (id: number, skill: Partial<Skill>) => {
    // dto validation
    delete skill.id;
    delete skill.parent;
    delete skill.parentId;
    delete skill.subs;
    delete skill.curriculum;
    const res = await api.patch(`${this.path}/${id}`, skill);
    return res.status;
  };

  static removeSubSkill = async (id: number, subSkillId: number) => {
    const res = await api.patch(
      `${this.path}/${id}/removeSubSkill/${subSkillId}`,
    );
    return res.data;
  };

  static removeSkill = async (id: number) => {
    const res = await api.delete(`${this.path}/${id}`);
    // ! console.log(res.data) not found
    return res.data;
  };

  static getAll = async (p?: Partial<QueryParams>) => {
    const res = await api.get<DataResponse<Skill>>(this.path, { params: p });
    return res.data;
  };

  static getOne = async (id: number) => {
    const res = await api.get<Skill>(`${this.path}/${id}`);
    return res.data;
  };

  static getOptions = async (curriculumId: number) => {
    const res = await api.get<Skill[]>(
      this.path + '/options' + '/' + curriculumId,
    );
    return { data: res.data, total: res.data.length };
  };
}

export default SkillService;
