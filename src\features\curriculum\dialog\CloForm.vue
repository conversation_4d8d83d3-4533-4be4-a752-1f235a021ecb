<template>
  <DialogForm
    v-model="store.dialogState"
    :title="store.form.id ? t('form.Edit CLO') : t('form.New CLO')"
    width="70%"
    :json="store.form"
    @save="store.handleSave(subject.id)"
    ref="formRef"
  >
    <div class="col-12 q-pa-sm">
      <q-input
        v-model="store.form.name"
        outlined
        dense
        :label="t('name')"
        :rules="[requireField]"
      >
      </q-input>
    </div>
    <!-- TH Detail -->
    <div class="col-12 q-pa-sm">
      <q-input
        v-model="store.form.thaiDescription"
        dense
        type="textarea"
        outlined
        counter
        maxlength="1000"
        :label="t('thaiDescription') + ' *'"
        :rules="[requireField]"
      />
    </div>
    <!-- ENG Detail -->
    <div class="col-12 q-pa-sm">
      <q-input
        v-model="store.form.engDescription"
        dense
        type="textarea"
        counter
        maxlength="1000"
        outlined
        :label="t('engDescription') + ' *'"
        :rules="[requireField]"
      />
    </div>
    <!-- SKill -->
    <div class="row">
      <div class="col-8 q-pa-sm">
        <FieldSearcher
          v-model="store.form.skillId"
          :label="t('skills')"
          :func-filter="updateFilteredSkills"
          :func-clear="clearSkill"
          :fetch-options="() => SkillService.getOptions(curriculumId)"
          @update:model-value="
            (s: any) => {
              formRef.validateForm();
            }
          "
        />
      </div>
      <div class="col-4 q-pa-sm">
        <q-select
          v-model="store.form.expectSkillLevel"
          :options="expectedLevelOptions"
          outlined
          dense
          :label="t('expectedLevel')"
          behavior="menu"
          @update:model-value="() => formRef.validateForm()"
          :rules="[requireField]"
        />
      </div>
    </div>
    <!-- Skill Card -->
    <div v-if="store.form.skill" class="row q-pb-md">
      <div class="col-12 q-pa-sm">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-h6">{{ store.form.skill?.thaiName }}</div>

            <div class="text-subtitle1">{{ store.form.skill?.engName }}</div>
          </q-card-section>
          <!-- ขยายขนาด -->
          <q-separator class="q-my-sm" />
          <!-- เส้นคั่น -->
          <q-card-section>
            <q-chip
              square
              color="primary"
              text-color="white"
              style="border-radius: 12px; padding: 6px 12px; text-align: center"
            >
              {{ store.form.skill?.domain }}
            </q-chip>
          </q-card-section>
          <q-card-section>
            <div
              class="text-body1 q-mb-md"
              style="
                white-space: pre-line;
                text-indent: 1.5em;
                word-wrap: break-word;
                overflow-wrap: break-word;
              "
            >
              {{ store.form.skill?.thaiDescription }}
            </div>
            <div
              class="text-body1 q-mb-md"
              style="
                white-space: pre-line;
                text-indent: 1.5em;
                word-wrap: break-word;
                overflow-wrap: break-word;
              "
            >
              {{ store.form.skill?.engDescription }}
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    <!-- PLO -->
    <div class="row">
      <div class="col q-pa-sm">
        <FieldSearcher
          v-model="store.form.ploId"
          label="PLO"
          :func-filter="updateFilteredPlos"
          :fetch-options="() => PloService.getOptions(curriculumId)"
          :func-clear="clearPLO"
          :option-label="'name'"
          @update:model-value="
            (s: any) => {
              formRef.validateForm();
            }
          "
        />
      </div>
    </div>

    <!-- PLO Card -->
    <div v-if="store.form.plo" class="row q-pb-md">
      <div class="col-12 q-pa-sm">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-h6">{{ store.form.plo?.name }}</div>
          </q-card-section>
          <!-- ขยายขนาด -->
          <q-separator class="q-my-sm" />
          <!-- เส้นคั่น -->
          <q-card-section>
            <div
              class="text-body1 q-mb-md"
              style="
                white-space: pre-line;
                text-indent: 1.5em;
                word-wrap: break-word;
                overflow-wrap: break-word;
              "
            >
              {{ store.form.plo?.thaiDescription }}
            </div>
            <div
              class="text-body1 q-mb-md"
              style="
                white-space: pre-line;
                text-indent: 1.5em;
                word-wrap: break-word;
                overflow-wrap: break-word;
              "
            >
              {{ store.form.plo?.engDescription }}
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import DialogForm from 'src/components/form/DialogForm.vue';
import { PloService } from 'src/services/plo';
import SkillService from 'src/services/skill';
import { useCloStore } from 'src/stores/clos';
import { usePloStore } from 'src/stores/plo';
import { useSkillStore } from 'src/stores/skill';
import { requireField } from 'src/utils/field-rules';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import FieldSearcher from 'src/components/form/FieldSearcher.vue';
import { useCurriculumStore } from 'src/stores/curriculum';
import type { Subject} from 'src/types/education';
import { LearningDomain, type PLO } from 'src/types/education';
// import { useRoute } from 'vue-router';

const formRef = ref();
const { t } = useI18n();
const store = useCloStore();
const ploStore = usePloStore();
const skillStore = useSkillStore();
const curriculumId = useCurriculumStore().getInsertId ?? -1;
const filteredPlos = ref([...ploStore.getListPLO]);
const filteredSkills = ref([...skillStore.getSkills]);

// const route = useRoute();
// const curriculumCode = route.params.code as string;

watch(
  () => store.form.skillId,
  async (id) => {
    if (!id) return;
    store.form.skill = await SkillService.getOne(id);
  },
);
watch(
  () => store.form.ploId,
  async (id) => {
    if (!id) return;
    store.form.plo = await PloService.getOne(id);
  },
);

defineProps<{
  subject: Subject;
}>();

const clearSkill = () => {
  store.form.skill = undefined;
};
const clearPLO = () => {
  store.form.plo = {} as PLO;
};

const updateFilteredPlos = (val: string, update: (cb: () => void) => void) => {
  update(() => {
    if (!val || val.trim() === '') {
      filteredPlos.value = [...ploStore.getListPLO];
    } else {
      filteredPlos.value = ploStore.getListPLO.filter((plo) =>
        plo.name.toLowerCase().includes(val.toLowerCase()),
      );
    }
  });
};
const updateFilteredSkills = (
  val: string,
  update: (cb: () => void) => void,
) => {
  update(() => {
    if (!val || val.trim() === '') {
      filteredSkills.value = [...skillStore.getSkills];
    } else {
      filteredSkills.value = skillStore.getSkills.filter((skill) =>
        skill.thaiName.toLowerCase().includes(val.toLowerCase()),
      );
    }
  });
};

const expectedLevelOptions = computed(() => {
  const domain = store.form.skill?.domain;
  return domain === LearningDomain.Psychomotor ||
    domain === LearningDomain.Cognitive
    ? [1, 2, 3, 4, 5]
    : [1, 2, 3];
});
</script>

<style scoped></style>
