<template>
  <DialogForm
    :title="store.form.id ? t('form.Edit Instructor') : t('form.New Instructor')"
    @save="store.handleSave"
    width="50%"
    :cta-text="store.getCtaText"
    :json="store.form"
  >
    <div class="row q-gutter-y-md">
      <FieldBranchOptions v-model:branch-id="store.form.branchId" />
      <q-input
        outlined
        dense
        v-model="store.form.email"
        :label="t('email')"
        type="email"
        clearable
        class="col-12"
        :rules="[requireField]"
      />
      <q-input
        outlined
        v-model="store.form.thaiName"
        :label="t('name')"
        clearable
        type="email"
        class="col-12"
        :rules="[requireField]"
        dense
      />
      <q-input
        outlined
        dense
        v-model="store.form.engName"
        :label="t('engName')"
        class="col-12"
        :rules="[requireField]"
        clearable
      />
      <q-input
        outlined
        dense
        v-model="store.form.code"
        :label="t('code')"
        clearable
        mask="########"
        class="col-12"
        hint="optional"
      />
      <q-select
        outlined
        dense
        v-model="store.form.position"
        :options="[...Object.values(AcademicRank)]"
        :label="t('position')"
        class="col-12"
        hint="optional"
        options-dense
      />
      <q-input
        outlined
        dense
        v-model="store.form.tel"
        :label="t('tel.')"
        clearable
        class="col-12"
        mask="###-###-####"
        unmasked-value
        hint="optional"
      />
      <q-input
        outlined
        dense
        v-model="store.form.officeRoom"
        :label="t('officeRoom')"
        hint="optional"
        class="col-12"
        clearable
      />
      <q-input
        outlined
        dense
        type="text"
        counter
        maxlength="150"
        hint="optional"
        v-model="store.form.specialists"
        :label="t('specialists')"
        class="col-12"
      />
      <q-input
        outlined
        dense
        v-model="store.form.bio"
        :label="t('bio')"
        class="col-12"
        type="textarea"
        hint="optional"
        counter
        maxlength="500"
      />
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import { useInstructorStore } from 'src/stores/instructor';
import DialogForm from 'src/components/form/DialogForm.vue';
import FieldBranchOptions from 'src/components/form/FieldBranchOptions.vue';
import { requireField } from 'src/utils/field-rules';
import { AcademicRank } from 'src/data/academic_rank';
import { useI18n } from 'vue-i18n';
const store = useInstructorStore();
const { t } = useI18n();
</script>

<style scoped></style>
