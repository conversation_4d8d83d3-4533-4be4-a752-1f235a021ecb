import { api } from 'boot/axios';
import type { DataResponse, QueryParams } from 'src/types/api';
import type { PLO } from 'src/types/education';
export class PloService {
  static path = 'plos';
  static async getAll(p?: Partial<QueryParams>) {
    const res = await api.get<DataResponse<PLO>>(this.path, { params: p });
    return res.data;
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`);
    return res.data;
  }

  static async createOne(obj: Partial<PLO>) {
    const res = await api.post(this.path, obj);
    return res;
  }

  static async updateOne(obj: Partial<PLO>) {
    delete obj.clos;
    delete obj.curriculumId;
    delete obj.curriculum;

    const res = await api.patch(`${this.path}/${obj.id}`, obj);
    return res;
  }

  static async removeOne(id: number) {
    const res = await api.delete(`${this.path}/${id}`);
    return res;
  }

  static getOptions = async (curriculumId: number) => {
    // const res = await api.get<PLO[]>(`${this.path}/options`, {
    //   params: { curriculumId },
    // });
    const res = await api.get<PLO[]>(
      this.path + '/options' + '/' + curriculumId,
    );
    return { data: res.data, total: res.data.length };
  };
}
