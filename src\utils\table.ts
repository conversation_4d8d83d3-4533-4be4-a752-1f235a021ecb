import { merge } from 'lodash';

export function updateTable<T extends { id: number }>(
  item: T,
  items: T[],
  method: 'create' | 'update' | 'delete',
): T[] {
  switch (method) {
    case 'delete': {
      const index = items.findIndex((existing) => existing.id === item.id);
      if (index !== -1) {
        return [...items.slice(0, index), ...items.slice(index + 1)];
      }
      return items;
    }

    case 'update': {
      const index = items.findIndex((existing) => existing.id === item.id);
      if (index !== -1) {
        const updatedItem = merge({}, items[index], item); // Deep merge
        return [
          ...items.slice(0, index),
          updatedItem,
          ...items.slice(index + 1),
        ];
      }
      return items;
    }

    case 'create':
      return [...items, item];

    default:
      return items;
  }
}
